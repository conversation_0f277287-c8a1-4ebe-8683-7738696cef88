from enum import IntFlag

import comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 as __wrapper_module__
from comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 import (
    CoClass, IUIAutomationGridItemPattern,
    UIA_IsLegacyIAccessiblePatternAvailablePropertyId,
    AnnotationType_Footer, UIA_StyleNameAttributeId,
    UIA_WindowPatternId, IUIAutomationTextChildPattern,
    UIA_OptimizeForVisualContentPropertyId, UIA_AppBarControlTypeId,
    UIA_AccessKeyPropertyId,
    UIA_IsScrollItemPatternAvailablePropertyId, BSTR,
    UIA_HostedFragmentRootsInvalidatedEventId, TextUnit_Document,
    Library, UIA_StrikethroughColorAttributeId, UIA_TextEditPatternId,
    HeadingLevel8, AnnotationType_Author,
    StructureChangeType_ChildrenReordered,
    UIA_SemanticZoomControlTypeId, UIA_FontWeightAttributeId,
    UIA_HeaderItemControlTypeId, IUIAutomationTreeWalker,
    UIA_VisualEffectsPropertyId,
    UIA_MultipleViewSupportedViewsPropertyId, UIA_ThumbControlTypeId,
    RowOrColumnMajor_Indeterminate, IUIAutomationDragPattern,
    AnnotationType_Sensitive, IUIAutomationMultipleViewPattern,
    UIA_ToolTipClosedEventId, DockPosition_Left,
    IUIAutomationElement3, IUIAutomationPropertyChangedEventHandler,
    AnnotationType_Endnote, UIA_TransformCanResizePropertyId,
    UIA_StructureChangedEventId, ZoomUnit_NoAmount,
    UIA_Selection2CurrentSelectedItemPropertyId, StyleId_Emphasis,
    HeadingLevel2, UIA_Selection2ItemCountPropertyId,
    IUIAutomationEventHandler, ZoomUnit_LargeDecrement,
    IUIAutomationElement8, AnnotationType_GrammarError,
    UIA_IsDataValidForFormPropertyId,
    UIA_LocalizedControlTypePropertyId, UIA_WindowIsTopmostPropertyId,
    UIA_GroupControlTypeId, UIA_OverlineStyleAttributeId,
    UIA_ExpandCollapseExpandCollapseStatePropertyId,
    UIA_TransformCanMovePropertyId, CUIAutomation8,
    UIA_ToolTipControlTypeId,
    UIA_ScrollHorizontalScrollPercentPropertyId,
    StructureChangeType_ChildrenBulkRemoved,
    UIA_StylesStyleNamePropertyId, UIA_Window_WindowClosedEventId,
    CoalesceEventsOptions_Disabled,
    IUIAutomationExpandCollapsePattern, UIA_DropTargetPatternId,
    UIA_SizeOfSetPropertyId, UIA_DragIsGrabbedPropertyId,
    UIA_BoundingRectanglePropertyId, IUIAutomationRangeValuePattern,
    UIA_ValuePatternId, UIA_ScrollPatternId,
    UIA_GridRowCountPropertyId, UIA_IsTablePatternAvailablePropertyId,
    UIA_ScrollBarControlTypeId,
    NotificationProcessing_ImportantMostRecent, TreeScope_Descendants,
    ProviderOptions_NonClientAreaProvider,
    IUIAutomationObjectModelPattern, Polite,
    SupportedTextSelection_Single, AnnotationType_Unknown,
    UIA_StylesFillColorPropertyId, UIA_FormLandmarkTypeId,
    UIA_IsDropTargetPatternAvailablePropertyId,
    UIA_RangeValueIsReadOnlyPropertyId, UIA_SliderControlTypeId,
    UIA_DescribedByPropertyId, IUIAutomation3,
    SynchronizedInputType_KeyUp, UIA_OutlineStylesAttributeId,
    StructureChangeType_ChildRemoved,
    IUIAutomationChangesEventHandler, StyleId_NumberedList,
    UIA_TabsAttributeId, UIA_TableControlTypeId,
    UIA_MenuClosedEventId, UIA_Transform2ZoomLevelPropertyId,
    TreeTraversalOptions_Default, TextUnit_Format, StyleId_Heading2,
    UIA_IsDialogPropertyId, CUIAutomation, IUIAutomationNotCondition,
    HeadingLevel6, ProviderOptions_RefuseNonClientSupport,
    UIA_EditControlTypeId, UIA_LegacyIAccessibleNamePropertyId,
    PropertyConditionFlags_IgnoreCase,
    UIA_RangeValueMaximumPropertyId, AnnotationType_MoveChange,
    UIA_LayoutInvalidatedEventId, IUIAutomationTextRange3,
    UIA_GridItemColumnPropertyId,
    UIA_IsStylesPatternAvailablePropertyId,
    IUIAutomationSelectionPattern2, UIA_ForegroundColorAttributeId,
    UIA_AnnotationAnnotationTypeNamePropertyId,
    ProviderOptions_HasNativeIAccessible,
    UIA_RangeValueValuePropertyId, IUIAutomationSelectionPattern,
    UIA_ToolTipOpenedEventId, UIA_StylesStyleIdPropertyId,
    UIA_WindowCanMinimizePropertyId, StyleId_Custom, _midlSAFEARRAY,
    IUIAutomation2, UIA_HeadingLevelPropertyId, UIA_TextPattern2Id,
    UIA_Selection2LastSelectedItemPropertyId, UIA_CapStyleAttributeId,
    WindowInteractionState_Closing,
    UIA_LegacyIAccessibleRolePropertyId,
    UIA_IsTextEditPatternAvailablePropertyId,
    UIA_IsRangeValuePatternAvailablePropertyId,
    StructureChangeType_ChildAdded, UIA_DropTarget_DragLeaveEventId,
    ProviderOptions_ServerSideProvider, VARIANT,
    UIA_IndentationFirstLineAttributeId,
    UIA_IsExpandCollapsePatternAvailablePropertyId, HeadingLevel7,
    UIA_ListControlTypeId, UIA_LabeledByPropertyId,
    UIA_DataGridControlTypeId, UIA_SpinnerControlTypeId,
    UIA_FillColorPropertyId, StyleId_Heading6,
    UIA_TextEdit_TextChangedEventId,
    UIA_TableItemColumnHeaderItemsPropertyId,
    UIA_IsMultipleViewPatternAvailablePropertyId,
    OrientationType_None, DockPosition_None,
    UIA_Transform2ZoomMaximumPropertyId,
    UIA_AutomationPropertyChangedEventId, UIA_MenuModeStartEventId,
    dispid, UIA_ScrollHorizontallyScrollablePropertyId,
    NotificationProcessing_ImportantCurrentThenMostRecent,
    UIA_SplitButtonControlTypeId,
    UIA_ActiveTextPositionChangedEventId,
    IUIAutomationTextEditPattern,
    UIA_SelectionItemSelectionContainerPropertyId,
    UIA_PositionInSetPropertyId, AnnotationType_ConflictingChange,
    NotificationProcessing_ImportantAll, IUIAutomation5,
    AnnotationType_TrackChanges,
    UIA_IsSynchronizedInputPatternAvailablePropertyId,
    UIA_IsTransformPatternAvailablePropertyId,
    UIA_LegacyIAccessibleValuePropertyId, UIA_ScrollItemPatternId,
    SynchronizedInputType_LeftMouseUp, UIA_TransformPattern2Id,
    UIA_VirtualizedItemPatternId, ToggleState_Off,
    OrientationType_Horizontal, IUIAutomationTextPattern,
    IUIAutomationCondition, UIA_TextPatternId,
    TextEditChangeType_AutoCorrect,
    UIA_IndentationTrailingAttributeId, UIA_NavigationLandmarkTypeId,
    HeadingLevel1, NotificationProcessing_All,
    UIA_AutomationIdPropertyId, UIA_BackgroundColorAttributeId,
    UIA_SelectionItem_ElementAddedToSelectionEventId,
    DockPosition_Right, UIA_ObjectModelPatternId,
    UIA_DocumentControlTypeId,
    UIA_LegacyIAccessibleDefaultActionPropertyId, StyleId_Title,
    UIA_DropTargetDropTargetEffectsPropertyId,
    IUIAutomationCacheRequest, UIA_SelectionItemIsSelectedPropertyId,
    UIA_StylesShapePropertyId, NotificationKind_ActionAborted,
    UIA_PaneControlTypeId, UIA_IsControlElementPropertyId,
    UIA_ValueValuePropertyId, UIA_TabItemControlTypeId,
    UIA_CustomLandmarkTypeId, ProviderOptions_UseClientCoordinates,
    IUIAutomationDropTargetPattern,
    UIA_GridItemContainingGridPropertyId, TextUnit_Page,
    IUIAutomationAnnotationPattern, AnnotationType_DeletionChange,
    IUIAutomationStructureChangedEventHandler,
    UIA_StatusBarControlTypeId,
    UIA_ScrollHorizontalViewSizePropertyId,
    UIA_BeforeParagraphSpacingAttributeId, UIA_ProcessIdPropertyId,
    COMMETHOD, AnnotationType_EditingLockedChange,
    IUIAutomationBoolCondition, DockPosition_Top,
    UIA_IsTableItemPatternAvailablePropertyId,
    UIA_CaretPositionAttributeId,
    UIA_IsSpreadsheetItemPatternAvailablePropertyId,
    UIA_FrameworkIdPropertyId, UIA_Transform2ZoomMinimumPropertyId,
    WindowVisualState_Minimized, UIA_ToolBarControlTypeId,
    UIA_MenuModeEndEventId, AutomationElementMode_Full,
    UIA_IsGridItemPatternAvailablePropertyId,
    IUIAutomationStylesPattern, StyleId_Heading1,
    PropertyConditionFlags_MatchSubstring, AnnotationType_Mathematics,
    UIA_Drag_DragCancelEventId, StyleId_Normal,
    UIA_CalendarControlTypeId,
    ConnectionRecoveryBehaviorOptions_Disabled,
    UIA_InputDiscardedEventId,
    UIA_IsObjectModelPatternAvailablePropertyId,
    UIA_InputReachedTargetEventId,
    UIA_SelectionIsSelectionRequiredPropertyId,
    ExpandCollapseState_Expanded, UIA_OverlineColorAttributeId,
    AnnotationType_SpellingError, TreeScope_None,
    UIA_ClickablePointPropertyId, AnnotationType_FormatChange,
    HeadingLevel5, OrientationType_Vertical,
    SynchronizedInputType_LeftMouseDown, IUIAutomationValuePattern,
    UIA_FontSizeAttributeId, UIA_AnnotationObjectsPropertyId,
    UIA_IsRequiredForFormPropertyId,
    IUIAutomationLegacyIAccessiblePattern,
    UIA_ControllerForPropertyId, NavigateDirection_PreviousSibling,
    NotificationKind_ItemAdded, UIA_IsSuperscriptAttributeId,
    UIA_IsAnnotationPatternAvailablePropertyId,
    NavigateDirection_NextSibling, IUIAutomationScrollPattern,
    UIA_ToggleToggleStatePropertyId, StyleId_Heading8,
    UIA_ControlTypePropertyId, UIA_IsSubscriptAttributeId,
    TextEditChangeType_AutoComplete, IUIAutomationElement7,
    TextUnit_Line,
    UIA_SelectionItem_ElementRemovedFromSelectionEventId,
    ZoomUnit_SmallIncrement,
    UIA_WindowWindowInteractionStatePropertyId,
    UIA_TitleBarControlTypeId,
    UIA_IsSelectionPattern2AvailablePropertyId,
    UIA_TableItemRowHeaderItemsPropertyId,
    UIA_ProviderDescriptionPropertyId,
    UIA_StrikethroughStyleAttributeId, UIA_RuntimeIdPropertyId,
    TextEditChangeType_None, IUIAutomationElement6,
    UIA_TransformPatternId, UIA_CaretBidiModeAttributeId, WSTRING,
    UIA_TogglePatternId, UIA_GridItemRowPropertyId,
    WindowInteractionState_ReadyForUserInteraction,
    ToggleState_Indeterminate, IUIAutomationTextPattern2,
    UIA_AnnotationTypesAttributeId, UIA_OutlineColorPropertyId,
    DockPosition_Fill, IUIAutomationTextEditTextChangedEventHandler,
    IUIAutomationSpreadsheetItemPattern,
    UIA_SynchronizedInputPatternId, HeadingLevel4, IUIAutomation4,
    AnnotationType_Comment,
    UIA_SpreadsheetItemAnnotationTypesPropertyId,
    NavigateDirection_LastChild, UIA_MenuOpenedEventId,
    IUIAutomationTogglePattern, UIA_TableItemPatternId,
    NotificationProcessing_CurrentThenMostRecent,
    IUIAutomationTextRange2,
    UIA_ScrollVerticalScrollPercentPropertyId, IUIAutomationElement4,
    UIA_SizePropertyId, UIA_StylesFillPatternColorPropertyId,
    IUIAutomationTransformPattern2, NotificationKind_ActionCompleted,
    UIA_HasKeyboardFocusPropertyId, HeadingLevel9,
    UIA_FlowsFromPropertyId,
    UIA_LegacyIAccessibleKeyboardShortcutPropertyId, StyleId_Heading3,
    Off, SynchronizedInputType_RightMouseDown,
    UIA_IndentationLeadingAttributeId,
    SupportedTextSelection_Multiple, IUIAutomationProxyFactoryEntry,
    IUIAutomationTextRangeArray, UIA_NativeWindowHandlePropertyId,
    UIA_StylesPatternId, UIA_SayAsInterpretAsAttributeId,
    UIA_IsScrollPatternAvailablePropertyId,
    UIA_SelectionActiveEndAttributeId, UIA_IsItalicAttributeId,
    ScrollAmount_LargeIncrement, tagPOINT,
    ExpandCollapseState_PartiallyExpanded, IUnknown,
    SupportedTextSelection_None, UIA_UnderlineColorAttributeId,
    IUIAutomationProxyFactoryMapping, IUIAutomationTableItemPattern,
    NavigateDirection_FirstChild, tagRECT,
    UIA_IsTransformPattern2AvailablePropertyId,
    UIA_ComboBoxControlTypeId, NotificationProcessing_MostRecent,
    TextEditChangeType_Composition, UIA_LevelPropertyId,
    UIA_SearchLandmarkTypeId, IUIAutomation6, UIA_ChangesEventId,
    AnnotationType_Highlighted, UIA_AnnotationTypesPropertyId,
    UIA_IsPeripheralPropertyId, UIA_TextChildPatternId, HeadingLevel3,
    UIA_ScrollVerticalViewSizePropertyId,
    UIA_Transform2CanZoomPropertyId, UIA_MarginLeadingAttributeId,
    UIA_ListItemControlTypeId, TreeScope_Subtree, StyleId_Heading9,
    UIA_Drag_DragStartEventId, UIA_StyleIdAttributeId,
    AnnotationType_InsertionChange,
    ProviderOptions_ProviderOwnsSetFocus, IRawElementProviderSimple,
    UIA_IsGridPatternAvailablePropertyId, UiaChangeInfo,
    IUIAutomationWindowPattern, UIA_LiveRegionChangedEventId,
    UIA_IsReadOnlyAttributeId, TextPatternRangeEndpoint_Start,
    UIA_IsTogglePatternAvailablePropertyId,
    UIA_WindowIsModalPropertyId, CoalesceEventsOptions_Enabled,
    UIA_SpreadsheetPatternId, UIA_ExpandCollapsePatternId,
    TreeScope_Element, UIA_DockDockPositionPropertyId,
    UIA_ItemStatusPropertyId, UIA_AriaRolePropertyId,
    UIA_Invoke_InvokedEventId, UIA_SelectionPatternId,
    UIA_AnnotationDateTimePropertyId, GUID,
    ScrollAmount_SmallIncrement,
    UIA_TextEdit_ConversionTargetChangedEventId,
    ProviderOptions_OverrideProvider,
    UIA_IsWindowPatternAvailablePropertyId, UIA_MenuBarControlTypeId,
    UIA_TransformCanRotatePropertyId,
    UIA_DropTargetDropTargetEffectPropertyId,
    IUIAutomationTablePattern,
    UIA_IsSelectionPatternAvailablePropertyId,
    UIA_TableRowOrColumnMajorPropertyId, WindowVisualState_Normal,
    TextUnit_Word, UIA_SayAsInterpretAsMetadataId,
    StructureChangeType_ChildrenBulkAdded,
    UIA_SelectionSelectionPropertyId, UIA_ProgressBarControlTypeId,
    UIA_LegacyIAccessiblePatternId, ProviderOptions_UseComThreading,
    ExpandCollapseState_LeafNode, UIA_HyperlinkControlTypeId,
    UIA_SpreadsheetItemFormulaPropertyId,
    UIA_DragGrabbedItemsPropertyId, UIA_NamePropertyId,
    UIA_TreeControlTypeId, IUIAutomationSelectionItemPattern,
    IUIAutomationSpreadsheetPattern, DockPosition_Bottom,
    IUIAutomationNotificationEventHandler, HRESULT,
    UIA_Text_TextChangedEventId,
    ConnectionRecoveryBehaviorOptions_Enabled, TreeScope_Parent,
    UIA_IsPasswordPropertyId, Assertive, UIA_CenterPointPropertyId,
    IUIAutomationTextRange, UIA_IsActiveAttributeId,
    UIA_LegacyIAccessibleHelpPropertyId, UIA_CustomControlTypeId,
    IUIAutomationElement2, StyleId_Heading4,
    RowOrColumnMajor_RowMajor, WindowInteractionState_Running,
    IUIAutomationElement5, UIA_GridPatternId, ZoomUnit_LargeIncrement,
    WindowInteractionState_NotResponding,
    UIA_TextFlowDirectionsAttributeId, StyleId_BulletedList,
    IUIAutomationSynchronizedInputPattern, UIA_HelpTextPropertyId,
    UIA_LinkAttributeId, IUIAutomationScrollItemPattern,
    UIA_MenuControlTypeId, TextUnit_Paragraph, typelib_path,
    UIA_AfterParagraphSpacingAttributeId, UIA_SeparatorControlTypeId,
    UIA_DragDropEffectPropertyId,
    UIA_IsTextPatternAvailablePropertyId,
    UIA_IsDockPatternAvailablePropertyId,
    IUIAutomationTransformPattern, IUIAutomation,
    IUIAutomationPropertyCondition, UIA_ItemContainerPatternId,
    UIA_CulturePropertyId, ScrollAmount_SmallDecrement,
    UIA_IsContentElementPropertyId, ScrollAmount_LargeDecrement,
    AnnotationType_CircularReferenceError, UIA_WindowControlTypeId,
    UIA_HeaderControlTypeId, UIA_LegacyIAccessibleSelectionPropertyId,
    UIA_HorizontalTextAlignmentAttributeId,
    WindowInteractionState_BlockedByModalWindow,
    TextPatternRangeEndpoint_End, UIA_AnnotationAuthorPropertyId,
    UIA_IsItemContainerPatternAvailablePropertyId,
    UIA_MenuItemControlTypeId, UIA_IsHiddenAttributeId,
    UIA_MarginBottomAttributeId, SynchronizedInputType_RightMouseUp,
    UIA_CustomNavigationPatternId, UIA_GridItemRowSpanPropertyId,
    UIA_OrientationPropertyId, UIA_DataItemControlTypeId,
    UIA_AnnotationAnnotationTypeIdPropertyId,
    UIA_TableRowHeadersPropertyId, UIA_ImageControlTypeId,
    UIA_SpreadsheetItemPatternId, UIA_AutomationFocusChangedEventId,
    UIA_SummaryChangeId, UIA_AriaPropertiesPropertyId,
    UIA_DropTarget_DragEnterEventId, UIA_AnnotationTargetPropertyId,
    UIA_StylesExtendedPropertiesPropertyId,
    UIA_AnnotationObjectsAttributeId, UIA_MainLandmarkTypeId,
    UIA_RotationPropertyId, UIA_FontNameAttributeId,
    UIA_LiveSettingPropertyId, UIA_AsyncContentLoadedEventId,
    UIA_DragPatternId, UIA_WindowWindowVisualStatePropertyId,
    NavigateDirection_Parent, UIA_IsKeyboardFocusablePropertyId,
    _check_version, StyleId_Quote,
    TextEditChangeType_CompositionFinalized, IUIAutomationElement9,
    UIA_SelectionItemPatternId, UIA_Drag_DragCompleteEventId,
    UIA_CultureAttributeId, UIA_WindowCanMaximizePropertyId,
    UIA_TablePatternId, UIA_Selection2FirstSelectedItemPropertyId,
    TextUnit_Character, ScrollAmount_NoAmount,
    StructureChangeType_ChildrenInvalidated,
    TreeTraversalOptions_PostOrder,
    UIA_IsDragPatternAvailablePropertyId, AnnotationType_FormulaError,
    IUIAutomationActiveTextPositionChangedEventHandler,
    UIA_RangeValueLargeChangePropertyId,
    UIA_IsSpreadsheetPatternAvailablePropertyId,
    UIA_RangeValueSmallChangePropertyId,
    ProviderOptions_ClientSideProvider, AnnotationType_ExternalChange,
    UIA_NotificationEventId,
    UIA_LegacyIAccessibleDescriptionPropertyId,
    UIA_OutlineThicknessPropertyId, UIA_TextControlTypeId,
    IAccessible, UIA_IsTextPattern2AvailablePropertyId,
    IUIAutomationDockPattern, UIA_StylesFillPatternStylePropertyId,
    _lcid, PropertyConditionFlags_None, UIA_ValueIsReadOnlyPropertyId,
    ExtendedProperty, IUIAutomationEventHandlerGroup,
    UIA_IsTextChildPatternAvailablePropertyId,
    UIA_SelectionPattern2Id, IUIAutomationElementArray,
    TreeTraversalOptions_LastToFirstOrder, UIA_IsEnabledPropertyId,
    TreeScope_Children,
    UIA_IsCustomNavigationPatternAvailablePropertyId,
    StyleId_Subtitle, HeadingLevel_None,
    UIA_SelectionItem_ElementSelectedEventId,
    UIA_TableColumnHeadersPropertyId,
    UIA_InputReachedOtherElementEventId,
    UIA_MarginTrailingAttributeId, UIA_DragDropEffectsPropertyId,
    UIA_LandmarkTypePropertyId, UIA_LineSpacingAttributeId,
    UIA_LegacyIAccessibleChildIdPropertyId,
    UIA_AcceleratorKeyPropertyId, IUIAutomationGridPattern,
    TreeScope_Ancestors, UIA_TreeItemControlTypeId, ToggleState_On,
    UIA_Text_TextSelectionChangedEventId, ZoomUnit_SmallDecrement,
    UIA_IsValuePatternAvailablePropertyId, StyleId_Heading7,
    IUIAutomationCustomNavigationPattern, UIA_InvokePatternId,
    NotificationKind_ItemRemoved, IUIAutomationElement,
    UIA_Selection_InvalidatedEventId, UIA_MultipleViewPatternId,
    IUIAutomationProxyFactory, UIA_DockPatternId,
    AnnotationType_Header, ExpandCollapseState_Collapsed,
    AnnotationType_AdvancedProofingIssue, StyleId_Heading5,
    NotificationKind_Other, UIA_FlowsToPropertyId,
    UIA_RangeValuePatternId,
    UIA_IsVirtualizedItemPatternAvailablePropertyId,
    IUIAutomationInvokePattern, AutomationElementMode_None,
    IUIAutomationAndCondition, UIA_SystemAlertEventId,
    IUIAutomationFocusChangedEventHandler,
    IUIAutomationItemContainerPattern, UIA_GridItemPatternId,
    UIA_SpreadsheetItemAnnotationObjectsPropertyId,
    UIA_FullDescriptionPropertyId,
    UIA_IsSelectionItemPatternAvailablePropertyId,
    SynchronizedInputType_KeyDown, UIA_ItemTypePropertyId,
    UIA_RangeValueMinimumPropertyId, UIA_DropTarget_DroppedEventId,
    AnnotationType_Footnote, UIA_LegacyIAccessibleStatePropertyId,
    UIA_FillTypePropertyId, UIA_SelectionCanSelectMultiplePropertyId,
    UIA_UnderlineStyleAttributeId,
    UIA_ScrollVerticallyScrollablePropertyId,
    UIA_RadioButtonControlTypeId, AnnotationType_DataValidationError,
    UIA_IsInvokePatternAvailablePropertyId, UIA_IsOffscreenPropertyId,
    UIA_GridItemColumnSpanPropertyId,
    IUIAutomationVirtualizedItemPattern, UIA_AnnotationPatternId,
    WindowVisualState_Maximized, UIA_TabControlTypeId,
    IUIAutomationOrCondition, UIA_ButtonControlTypeId,
    UIA_LocalizedLandmarkTypePropertyId, UIA_BulletStyleAttributeId,
    UIA_MarginTopAttributeId, UIA_Window_WindowOpenedEventId,
    UIA_AnimationStyleAttributeId,
    UIA_MultipleViewCurrentViewPropertyId,
    AnnotationType_UnsyncedChange, UIA_CheckBoxControlTypeId,
    RowOrColumnMajor_ColumnMajor, UIA_ClassNamePropertyId, IDispatch,
    UIA_GridColumnCountPropertyId
)


class TextPatternRangeEndpoint(IntFlag):
    TextPatternRangeEndpoint_Start = 0
    TextPatternRangeEndpoint_End = 1


class TextUnit(IntFlag):
    TextUnit_Character = 0
    TextUnit_Format = 1
    TextUnit_Word = 2
    TextUnit_Line = 3
    TextUnit_Paragraph = 4
    TextUnit_Page = 5
    TextUnit_Document = 6


class WindowVisualState(IntFlag):
    WindowVisualState_Normal = 0
    WindowVisualState_Maximized = 1
    WindowVisualState_Minimized = 2


class WindowInteractionState(IntFlag):
    WindowInteractionState_Running = 0
    WindowInteractionState_Closing = 1
    WindowInteractionState_ReadyForUserInteraction = 2
    WindowInteractionState_BlockedByModalWindow = 3
    WindowInteractionState_NotResponding = 4


class TreeScope(IntFlag):
    TreeScope_None = 0
    TreeScope_Element = 1
    TreeScope_Children = 2
    TreeScope_Descendants = 4
    TreeScope_Parent = 8
    TreeScope_Ancestors = 16
    TreeScope_Subtree = 7


class TextEditChangeType(IntFlag):
    TextEditChangeType_None = 0
    TextEditChangeType_AutoCorrect = 1
    TextEditChangeType_Composition = 2
    TextEditChangeType_CompositionFinalized = 3
    TextEditChangeType_AutoComplete = 4


class PropertyConditionFlags(IntFlag):
    PropertyConditionFlags_None = 0
    PropertyConditionFlags_IgnoreCase = 1
    PropertyConditionFlags_MatchSubstring = 2


class SupportedTextSelection(IntFlag):
    SupportedTextSelection_None = 0
    SupportedTextSelection_Single = 1
    SupportedTextSelection_Multiple = 2


class NavigateDirection(IntFlag):
    NavigateDirection_Parent = 0
    NavigateDirection_NextSibling = 1
    NavigateDirection_PreviousSibling = 2
    NavigateDirection_FirstChild = 3
    NavigateDirection_LastChild = 4


class StructureChangeType(IntFlag):
    StructureChangeType_ChildAdded = 0
    StructureChangeType_ChildRemoved = 1
    StructureChangeType_ChildrenInvalidated = 2
    StructureChangeType_ChildrenBulkAdded = 3
    StructureChangeType_ChildrenBulkRemoved = 4
    StructureChangeType_ChildrenReordered = 5


class ScrollAmount(IntFlag):
    ScrollAmount_LargeDecrement = 0
    ScrollAmount_SmallDecrement = 1
    ScrollAmount_NoAmount = 2
    ScrollAmount_LargeIncrement = 3
    ScrollAmount_SmallIncrement = 4


class OrientationType(IntFlag):
    OrientationType_None = 0
    OrientationType_Horizontal = 1
    OrientationType_Vertical = 2


class LiveSetting(IntFlag):
    Off = 0
    Polite = 1
    Assertive = 2


class NotificationKind(IntFlag):
    NotificationKind_ItemAdded = 0
    NotificationKind_ItemRemoved = 1
    NotificationKind_ActionCompleted = 2
    NotificationKind_ActionAborted = 3
    NotificationKind_Other = 4


class NotificationProcessing(IntFlag):
    NotificationProcessing_ImportantAll = 0
    NotificationProcessing_ImportantMostRecent = 1
    NotificationProcessing_All = 2
    NotificationProcessing_MostRecent = 3
    NotificationProcessing_CurrentThenMostRecent = 4
    NotificationProcessing_ImportantCurrentThenMostRecent = 5


class DockPosition(IntFlag):
    DockPosition_Top = 0
    DockPosition_Left = 1
    DockPosition_Bottom = 2
    DockPosition_Right = 3
    DockPosition_Fill = 4
    DockPosition_None = 5


class AutomationElementMode(IntFlag):
    AutomationElementMode_None = 0
    AutomationElementMode_Full = 1


class ExpandCollapseState(IntFlag):
    ExpandCollapseState_Collapsed = 0
    ExpandCollapseState_Expanded = 1
    ExpandCollapseState_PartiallyExpanded = 2
    ExpandCollapseState_LeafNode = 3


class SynchronizedInputType(IntFlag):
    SynchronizedInputType_KeyUp = 1
    SynchronizedInputType_KeyDown = 2
    SynchronizedInputType_LeftMouseUp = 4
    SynchronizedInputType_LeftMouseDown = 8
    SynchronizedInputType_RightMouseUp = 16
    SynchronizedInputType_RightMouseDown = 32


class RowOrColumnMajor(IntFlag):
    RowOrColumnMajor_RowMajor = 0
    RowOrColumnMajor_ColumnMajor = 1
    RowOrColumnMajor_Indeterminate = 2


class ConnectionRecoveryBehaviorOptions(IntFlag):
    ConnectionRecoveryBehaviorOptions_Disabled = 0
    ConnectionRecoveryBehaviorOptions_Enabled = 1


class CoalesceEventsOptions(IntFlag):
    CoalesceEventsOptions_Disabled = 0
    CoalesceEventsOptions_Enabled = 1


class TreeTraversalOptions(IntFlag):
    TreeTraversalOptions_Default = 0
    TreeTraversalOptions_PostOrder = 1
    TreeTraversalOptions_LastToFirstOrder = 2


class ToggleState(IntFlag):
    ToggleState_Off = 0
    ToggleState_On = 1
    ToggleState_Indeterminate = 2


class ZoomUnit(IntFlag):
    ZoomUnit_NoAmount = 0
    ZoomUnit_LargeDecrement = 1
    ZoomUnit_SmallDecrement = 2
    ZoomUnit_LargeIncrement = 3
    ZoomUnit_SmallIncrement = 4


class ProviderOptions(IntFlag):
    ProviderOptions_ClientSideProvider = 1
    ProviderOptions_ServerSideProvider = 2
    ProviderOptions_NonClientAreaProvider = 4
    ProviderOptions_OverrideProvider = 8
    ProviderOptions_ProviderOwnsSetFocus = 16
    ProviderOptions_UseComThreading = 32
    ProviderOptions_RefuseNonClientSupport = 64
    ProviderOptions_HasNativeIAccessible = 128
    ProviderOptions_UseClientCoordinates = 256


__all__ = [
    'IUIAutomationGridItemPattern', 'NotificationKind',
    'UIA_IsLegacyIAccessiblePatternAvailablePropertyId',
    'AnnotationType_Footer', 'UIA_StyleNameAttributeId',
    'UIA_WindowPatternId', 'IUIAutomationTextChildPattern',
    'UIA_OptimizeForVisualContentPropertyId',
    'UIA_AppBarControlTypeId', 'UIA_AccessKeyPropertyId',
    'UIA_IsScrollItemPatternAvailablePropertyId',
    'UIA_HostedFragmentRootsInvalidatedEventId', 'TextUnit_Document',
    'Library', 'UIA_StrikethroughColorAttributeId',
    'UIA_TextEditPatternId', 'HeadingLevel8', 'AnnotationType_Author',
    'StructureChangeType_ChildrenReordered',
    'UIA_SemanticZoomControlTypeId', 'UIA_FontWeightAttributeId',
    'UIA_HeaderItemControlTypeId', 'IUIAutomationTreeWalker',
    'UIA_VisualEffectsPropertyId',
    'UIA_MultipleViewSupportedViewsPropertyId',
    'UIA_ThumbControlTypeId', 'RowOrColumnMajor_Indeterminate',
    'IUIAutomationDragPattern', 'AnnotationType_Sensitive',
    'IUIAutomationMultipleViewPattern', 'UIA_ToolTipClosedEventId',
    'DockPosition_Left', 'IUIAutomationElement3',
    'IUIAutomationPropertyChangedEventHandler',
    'AnnotationType_Endnote', 'UIA_TransformCanResizePropertyId',
    'UIA_StructureChangedEventId', 'ZoomUnit_NoAmount',
    'UIA_Selection2CurrentSelectedItemPropertyId', 'StyleId_Emphasis',
    'HeadingLevel2', 'UIA_Selection2ItemCountPropertyId',
    'IUIAutomationEventHandler', 'ZoomUnit_LargeDecrement',
    'IUIAutomationElement8', 'AnnotationType_GrammarError',
    'UIA_IsDataValidForFormPropertyId',
    'UIA_LocalizedControlTypePropertyId',
    'UIA_WindowIsTopmostPropertyId', 'UIA_GroupControlTypeId',
    'UIA_OverlineStyleAttributeId',
    'UIA_ExpandCollapseExpandCollapseStatePropertyId',
    'UIA_TransformCanMovePropertyId', 'CUIAutomation8',
    'UIA_ToolTipControlTypeId',
    'UIA_ScrollHorizontalScrollPercentPropertyId',
    'StructureChangeType_ChildrenBulkRemoved',
    'UIA_StylesStyleNamePropertyId', 'UIA_Window_WindowClosedEventId',
    'CoalesceEventsOptions_Disabled',
    'IUIAutomationExpandCollapsePattern', 'UIA_DropTargetPatternId',
    'UIA_SizeOfSetPropertyId', 'UIA_DragIsGrabbedPropertyId',
    'UIA_BoundingRectanglePropertyId',
    'IUIAutomationRangeValuePattern', 'UIA_ValuePatternId',
    'UIA_ScrollPatternId', 'UIA_GridRowCountPropertyId',
    'UIA_IsTablePatternAvailablePropertyId',
    'UIA_ScrollBarControlTypeId',
    'NotificationProcessing_ImportantMostRecent',
    'TreeScope_Descendants', 'ProviderOptions_NonClientAreaProvider',
    'IUIAutomationObjectModelPattern', 'Polite',
    'SupportedTextSelection_Single', 'AnnotationType_Unknown',
    'UIA_StylesFillColorPropertyId', 'UIA_FormLandmarkTypeId',
    'UIA_IsDropTargetPatternAvailablePropertyId',
    'UIA_RangeValueIsReadOnlyPropertyId', 'UIA_SliderControlTypeId',
    'UIA_DescribedByPropertyId', 'IUIAutomation3',
    'SynchronizedInputType_KeyUp', 'UIA_OutlineStylesAttributeId',
    'StructureChangeType_ChildRemoved',
    'IUIAutomationChangesEventHandler', 'StyleId_NumberedList',
    'UIA_TabsAttributeId', 'UIA_TableControlTypeId',
    'UIA_MenuClosedEventId', 'UIA_Transform2ZoomLevelPropertyId',
    'ScrollAmount', 'TreeTraversalOptions_Default', 'TextUnit_Format',
    'StyleId_Heading2', 'UIA_IsDialogPropertyId', 'CUIAutomation',
    'IUIAutomationNotCondition', 'HeadingLevel6',
    'ProviderOptions_RefuseNonClientSupport', 'UIA_EditControlTypeId',
    'UIA_LegacyIAccessibleNamePropertyId',
    'PropertyConditionFlags_IgnoreCase',
    'UIA_RangeValueMaximumPropertyId', 'AnnotationType_MoveChange',
    'UIA_LayoutInvalidatedEventId', 'IUIAutomationTextRange3',
    'UIA_GridItemColumnPropertyId',
    'UIA_IsStylesPatternAvailablePropertyId',
    'IUIAutomationSelectionPattern2',
    'UIA_ForegroundColorAttributeId',
    'UIA_AnnotationAnnotationTypeNamePropertyId',
    'ProviderOptions_HasNativeIAccessible', 'WindowVisualState',
    'UIA_RangeValueValuePropertyId', 'IUIAutomationSelectionPattern',
    'UIA_ToolTipOpenedEventId', 'UIA_StylesStyleIdPropertyId',
    'UIA_WindowCanMinimizePropertyId', 'StyleId_Custom',
    'IUIAutomation2', 'UIA_HeadingLevelPropertyId',
    'UIA_TextPattern2Id', 'UIA_Selection2LastSelectedItemPropertyId',
    'UIA_CapStyleAttributeId', 'WindowInteractionState_Closing',
    'UIA_LegacyIAccessibleRolePropertyId',
    'UIA_IsTextEditPatternAvailablePropertyId',
    'UIA_IsRangeValuePatternAvailablePropertyId',
    'StructureChangeType_ChildAdded',
    'UIA_DropTarget_DragLeaveEventId',
    'ProviderOptions_ServerSideProvider',
    'UIA_IndentationFirstLineAttributeId',
    'UIA_IsExpandCollapsePatternAvailablePropertyId', 'HeadingLevel7',
    'UIA_ListControlTypeId', 'UIA_LabeledByPropertyId',
    'UIA_DataGridControlTypeId', 'UIA_SpinnerControlTypeId',
    'UIA_FillColorPropertyId', 'StyleId_Heading6',
    'UIA_TextEdit_TextChangedEventId',
    'UIA_TableItemColumnHeaderItemsPropertyId',
    'PropertyConditionFlags',
    'UIA_IsMultipleViewPatternAvailablePropertyId',
    'OrientationType_None', 'DockPosition_None',
    'UIA_Transform2ZoomMaximumPropertyId',
    'UIA_AutomationPropertyChangedEventId',
    'UIA_MenuModeStartEventId',
    'UIA_ScrollHorizontallyScrollablePropertyId',
    'NotificationProcessing_ImportantCurrentThenMostRecent',
    'UIA_SplitButtonControlTypeId',
    'UIA_ActiveTextPositionChangedEventId',
    'IUIAutomationTextEditPattern',
    'UIA_SelectionItemSelectionContainerPropertyId',
    'SupportedTextSelection', 'UIA_PositionInSetPropertyId',
    'AnnotationType_ConflictingChange',
    'NotificationProcessing_ImportantAll', 'IUIAutomation5',
    'AnnotationType_TrackChanges',
    'UIA_IsSynchronizedInputPatternAvailablePropertyId',
    'UIA_IsTransformPatternAvailablePropertyId',
    'UIA_LegacyIAccessibleValuePropertyId', 'UIA_ScrollItemPatternId',
    'SynchronizedInputType_LeftMouseUp', 'UIA_TransformPattern2Id',
    'UIA_VirtualizedItemPatternId', 'ToggleState_Off',
    'OrientationType_Horizontal', 'IUIAutomationTextPattern',
    'IUIAutomationCondition', 'UIA_TextPatternId',
    'TextEditChangeType_AutoCorrect',
    'UIA_IndentationTrailingAttributeId',
    'UIA_NavigationLandmarkTypeId', 'HeadingLevel1',
    'NotificationProcessing_All', 'UIA_AutomationIdPropertyId',
    'UIA_BackgroundColorAttributeId',
    'UIA_SelectionItem_ElementAddedToSelectionEventId',
    'DockPosition_Right', 'UIA_ObjectModelPatternId',
    'UIA_DocumentControlTypeId',
    'UIA_LegacyIAccessibleDefaultActionPropertyId', 'StyleId_Title',
    'UIA_DropTargetDropTargetEffectsPropertyId',
    'IUIAutomationCacheRequest',
    'UIA_SelectionItemIsSelectedPropertyId',
    'UIA_StylesShapePropertyId', 'NotificationKind_ActionAborted',
    'TextEditChangeType', 'UIA_PaneControlTypeId',
    'UIA_IsControlElementPropertyId', 'UIA_ValueValuePropertyId',
    'UIA_TabItemControlTypeId', 'UIA_CustomLandmarkTypeId',
    'ProviderOptions_UseClientCoordinates',
    'IUIAutomationDropTargetPattern',
    'UIA_GridItemContainingGridPropertyId', 'CoalesceEventsOptions',
    'TextUnit_Page', 'IUIAutomationAnnotationPattern',
    'AnnotationType_DeletionChange',
    'IUIAutomationStructureChangedEventHandler',
    'UIA_StatusBarControlTypeId',
    'UIA_ScrollHorizontalViewSizePropertyId',
    'UIA_BeforeParagraphSpacingAttributeId',
    'UIA_ProcessIdPropertyId', 'AnnotationType_EditingLockedChange',
    'IUIAutomationBoolCondition', 'DockPosition_Top',
    'UIA_IsTableItemPatternAvailablePropertyId',
    'UIA_CaretPositionAttributeId',
    'UIA_IsSpreadsheetItemPatternAvailablePropertyId',
    'UIA_FrameworkIdPropertyId',
    'UIA_Transform2ZoomMinimumPropertyId',
    'WindowVisualState_Minimized', 'UIA_ToolBarControlTypeId',
    'UIA_MenuModeEndEventId', 'AutomationElementMode_Full',
    'UIA_IsGridItemPatternAvailablePropertyId',
    'IUIAutomationStylesPattern', 'StyleId_Heading1',
    'PropertyConditionFlags_MatchSubstring',
    'AnnotationType_Mathematics', 'UIA_Drag_DragCancelEventId',
    'StyleId_Normal', 'UIA_CalendarControlTypeId', 'RowOrColumnMajor',
    'ExpandCollapseState',
    'ConnectionRecoveryBehaviorOptions_Disabled',
    'UIA_InputDiscardedEventId',
    'UIA_IsObjectModelPatternAvailablePropertyId',
    'UIA_InputReachedTargetEventId',
    'UIA_SelectionIsSelectionRequiredPropertyId',
    'ExpandCollapseState_Expanded', 'UIA_OverlineColorAttributeId',
    'ToggleState', 'AnnotationType_SpellingError', 'TreeScope_None',
    'DockPosition', 'UIA_ClickablePointPropertyId',
    'AnnotationType_FormatChange', 'HeadingLevel5',
    'OrientationType_Vertical', 'SynchronizedInputType_LeftMouseDown',
    'IUIAutomationValuePattern', 'UIA_FontSizeAttributeId',
    'UIA_AnnotationObjectsPropertyId',
    'UIA_IsRequiredForFormPropertyId',
    'IUIAutomationLegacyIAccessiblePattern',
    'UIA_ControllerForPropertyId',
    'NavigateDirection_PreviousSibling', 'NotificationKind_ItemAdded',
    'UIA_IsSuperscriptAttributeId',
    'UIA_IsAnnotationPatternAvailablePropertyId',
    'NavigateDirection_NextSibling', 'IUIAutomationScrollPattern',
    'UIA_ToggleToggleStatePropertyId', 'StyleId_Heading8',
    'UIA_ControlTypePropertyId', 'UIA_IsSubscriptAttributeId',
    'TextEditChangeType_AutoComplete', 'IUIAutomationElement7',
    'TreeTraversalOptions', 'TextUnit_Line',
    'UIA_SelectionItem_ElementRemovedFromSelectionEventId',
    'ZoomUnit_SmallIncrement',
    'UIA_WindowWindowInteractionStatePropertyId',
    'UIA_TitleBarControlTypeId',
    'UIA_IsSelectionPattern2AvailablePropertyId',
    'UIA_TableItemRowHeaderItemsPropertyId',
    'UIA_ProviderDescriptionPropertyId',
    'ConnectionRecoveryBehaviorOptions',
    'UIA_StrikethroughStyleAttributeId', 'UIA_RuntimeIdPropertyId',
    'TextEditChangeType_None', 'IUIAutomationElement6',
    'UIA_TransformPatternId', 'UIA_CaretBidiModeAttributeId',
    'UIA_TogglePatternId', 'UIA_GridItemRowPropertyId',
    'WindowInteractionState_ReadyForUserInteraction',
    'ToggleState_Indeterminate', 'IUIAutomationTextPattern2',
    'UIA_AnnotationTypesAttributeId', 'UIA_OutlineColorPropertyId',
    'DockPosition_Fill',
    'IUIAutomationTextEditTextChangedEventHandler',
    'IUIAutomationSpreadsheetItemPattern',
    'UIA_SynchronizedInputPatternId', 'HeadingLevel4',
    'IUIAutomation4', 'AnnotationType_Comment',
    'UIA_SpreadsheetItemAnnotationTypesPropertyId',
    'NavigateDirection_LastChild', 'UIA_MenuOpenedEventId',
    'IUIAutomationTogglePattern', 'UIA_TableItemPatternId',
    'NotificationProcessing_CurrentThenMostRecent',
    'IUIAutomationTextRange2',
    'UIA_ScrollVerticalScrollPercentPropertyId',
    'IUIAutomationElement4', 'UIA_SizePropertyId',
    'UIA_StylesFillPatternColorPropertyId',
    'IUIAutomationTransformPattern2',
    'NotificationKind_ActionCompleted',
    'UIA_HasKeyboardFocusPropertyId', 'HeadingLevel9',
    'UIA_FlowsFromPropertyId',
    'UIA_LegacyIAccessibleKeyboardShortcutPropertyId',
    'StyleId_Heading3', 'Off', 'SynchronizedInputType_RightMouseDown',
    'UIA_IndentationLeadingAttributeId',
    'SupportedTextSelection_Multiple',
    'IUIAutomationProxyFactoryEntry', 'IUIAutomationTextRangeArray',
    'UIA_NativeWindowHandlePropertyId', 'UIA_StylesPatternId',
    'UIA_SayAsInterpretAsAttributeId',
    'UIA_IsScrollPatternAvailablePropertyId',
    'UIA_SelectionActiveEndAttributeId', 'NavigateDirection',
    'UIA_IsItalicAttributeId', 'ScrollAmount_LargeIncrement',
    'ExpandCollapseState_PartiallyExpanded',
    'SupportedTextSelection_None', 'UIA_UnderlineColorAttributeId',
    'IUIAutomationProxyFactoryMapping',
    'IUIAutomationTableItemPattern', 'NavigateDirection_FirstChild',
    'UIA_IsTransformPattern2AvailablePropertyId',
    'UIA_ComboBoxControlTypeId', 'NotificationProcessing_MostRecent',
    'TextEditChangeType_Composition', 'UIA_LevelPropertyId',
    'UIA_SearchLandmarkTypeId', 'IUIAutomation6',
    'UIA_ChangesEventId', 'AnnotationType_Highlighted',
    'UIA_AnnotationTypesPropertyId', 'UIA_IsPeripheralPropertyId',
    'UIA_TextChildPatternId', 'HeadingLevel3',
    'UIA_ScrollVerticalViewSizePropertyId',
    'UIA_Transform2CanZoomPropertyId', 'UIA_MarginLeadingAttributeId',
    'UIA_ListItemControlTypeId', 'TreeScope_Subtree',
    'StyleId_Heading9', 'UIA_Drag_DragStartEventId',
    'UIA_StyleIdAttributeId', 'AnnotationType_InsertionChange',
    'ProviderOptions_ProviderOwnsSetFocus',
    'IRawElementProviderSimple',
    'UIA_IsGridPatternAvailablePropertyId', 'UiaChangeInfo',
    'IUIAutomationWindowPattern', 'UIA_LiveRegionChangedEventId',
    'UIA_IsReadOnlyAttributeId', 'TextPatternRangeEndpoint_Start',
    'UIA_IsTogglePatternAvailablePropertyId',
    'UIA_WindowIsModalPropertyId', 'CoalesceEventsOptions_Enabled',
    'UIA_SpreadsheetPatternId', 'SynchronizedInputType',
    'UIA_ExpandCollapsePatternId', 'TreeScope_Element',
    'UIA_DockDockPositionPropertyId', 'UIA_ItemStatusPropertyId',
    'UIA_AriaRolePropertyId', 'UIA_Invoke_InvokedEventId',
    'UIA_SelectionPatternId', 'UIA_AnnotationDateTimePropertyId',
    'ScrollAmount_SmallIncrement',
    'UIA_TextEdit_ConversionTargetChangedEventId',
    'ProviderOptions_OverrideProvider',
    'UIA_IsWindowPatternAvailablePropertyId',
    'UIA_MenuBarControlTypeId', 'UIA_TransformCanRotatePropertyId',
    'UIA_DropTargetDropTargetEffectPropertyId',
    'IUIAutomationTablePattern',
    'UIA_IsSelectionPatternAvailablePropertyId',
    'UIA_TableRowOrColumnMajorPropertyId', 'WindowVisualState_Normal',
    'TextUnit_Word', 'UIA_SayAsInterpretAsMetadataId',
    'StructureChangeType_ChildrenBulkAdded',
    'UIA_SelectionSelectionPropertyId',
    'UIA_ProgressBarControlTypeId', 'UIA_LegacyIAccessiblePatternId',
    'ProviderOptions_UseComThreading', 'TextUnit',
    'ExpandCollapseState_LeafNode', 'UIA_HyperlinkControlTypeId',
    'UIA_SpreadsheetItemFormulaPropertyId',
    'UIA_DragGrabbedItemsPropertyId', 'UIA_NamePropertyId',
    'UIA_TreeControlTypeId', 'IUIAutomationSelectionItemPattern',
    'IUIAutomationSpreadsheetPattern', 'DockPosition_Bottom',
    'IUIAutomationNotificationEventHandler',
    'UIA_Text_TextChangedEventId',
    'ConnectionRecoveryBehaviorOptions_Enabled', 'TreeScope_Parent',
    'UIA_IsPasswordPropertyId', 'Assertive',
    'UIA_CenterPointPropertyId', 'IUIAutomationTextRange',
    'UIA_IsActiveAttributeId', 'UIA_LegacyIAccessibleHelpPropertyId',
    'UIA_CustomControlTypeId', 'IUIAutomationElement2',
    'StyleId_Heading4', 'RowOrColumnMajor_RowMajor',
    'WindowInteractionState_Running', 'IUIAutomationElement5',
    'UIA_GridPatternId', 'ZoomUnit_LargeIncrement',
    'WindowInteractionState_NotResponding',
    'UIA_TextFlowDirectionsAttributeId', 'LiveSetting',
    'StyleId_BulletedList', 'IUIAutomationSynchronizedInputPattern',
    'UIA_HelpTextPropertyId', 'UIA_LinkAttributeId',
    'IUIAutomationScrollItemPattern', 'UIA_MenuControlTypeId',
    'TextUnit_Paragraph', 'typelib_path',
    'UIA_AfterParagraphSpacingAttributeId',
    'UIA_SeparatorControlTypeId', 'UIA_DragDropEffectPropertyId',
    'UIA_IsTextPatternAvailablePropertyId',
    'UIA_IsDockPatternAvailablePropertyId',
    'IUIAutomationTransformPattern', 'IUIAutomation',
    'IUIAutomationPropertyCondition', 'UIA_ItemContainerPatternId',
    'UIA_CulturePropertyId', 'ScrollAmount_SmallDecrement',
    'UIA_IsContentElementPropertyId', 'ScrollAmount_LargeDecrement',
    'AnnotationType_CircularReferenceError',
    'UIA_WindowControlTypeId', 'UIA_HeaderControlTypeId',
    'UIA_LegacyIAccessibleSelectionPropertyId',
    'UIA_HorizontalTextAlignmentAttributeId',
    'WindowInteractionState_BlockedByModalWindow',
    'WindowInteractionState', 'TextPatternRangeEndpoint_End',
    'UIA_AnnotationAuthorPropertyId',
    'UIA_IsItemContainerPatternAvailablePropertyId',
    'UIA_MenuItemControlTypeId', 'UIA_IsHiddenAttributeId',
    'UIA_MarginBottomAttributeId', 'ZoomUnit',
    'SynchronizedInputType_RightMouseUp',
    'UIA_CustomNavigationPatternId', 'UIA_GridItemRowSpanPropertyId',
    'UIA_OrientationPropertyId', 'UIA_DataItemControlTypeId',
    'UIA_AnnotationAnnotationTypeIdPropertyId',
    'UIA_TableRowHeadersPropertyId', 'UIA_ImageControlTypeId',
    'UIA_SpreadsheetItemPatternId',
    'UIA_AutomationFocusChangedEventId', 'UIA_SummaryChangeId',
    'UIA_AriaPropertiesPropertyId', 'UIA_DropTarget_DragEnterEventId',
    'UIA_AnnotationTargetPropertyId',
    'UIA_StylesExtendedPropertiesPropertyId',
    'UIA_AnnotationObjectsAttributeId', 'UIA_MainLandmarkTypeId',
    'UIA_RotationPropertyId', 'UIA_FontNameAttributeId',
    'UIA_LiveSettingPropertyId', 'UIA_AsyncContentLoadedEventId',
    'UIA_DragPatternId', 'UIA_WindowWindowVisualStatePropertyId',
    'TreeScope', 'NavigateDirection_Parent',
    'UIA_IsKeyboardFocusablePropertyId', 'StyleId_Quote',
    'TextEditChangeType_CompositionFinalized',
    'IUIAutomationElement9', 'UIA_SelectionItemPatternId',
    'UIA_Drag_DragCompleteEventId', 'UIA_CultureAttributeId',
    'UIA_WindowCanMaximizePropertyId', 'UIA_TablePatternId',
    'UIA_Selection2FirstSelectedItemPropertyId', 'TextUnit_Character',
    'ScrollAmount_NoAmount',
    'StructureChangeType_ChildrenInvalidated',
    'TreeTraversalOptions_PostOrder',
    'UIA_IsDragPatternAvailablePropertyId',
    'AnnotationType_FormulaError',
    'IUIAutomationActiveTextPositionChangedEventHandler',
    'UIA_RangeValueLargeChangePropertyId',
    'UIA_IsSpreadsheetPatternAvailablePropertyId',
    'UIA_RangeValueSmallChangePropertyId',
    'ProviderOptions_ClientSideProvider',
    'AnnotationType_ExternalChange', 'UIA_NotificationEventId',
    'UIA_LegacyIAccessibleDescriptionPropertyId',
    'AutomationElementMode', 'UIA_OutlineThicknessPropertyId',
    'UIA_TextControlTypeId', 'IAccessible',
    'UIA_IsTextPattern2AvailablePropertyId',
    'IUIAutomationDockPattern',
    'UIA_StylesFillPatternStylePropertyId',
    'PropertyConditionFlags_None', 'UIA_ValueIsReadOnlyPropertyId',
    'ExtendedProperty', 'ProviderOptions',
    'IUIAutomationEventHandlerGroup',
    'UIA_IsTextChildPatternAvailablePropertyId',
    'UIA_SelectionPattern2Id', 'IUIAutomationElementArray',
    'TreeTraversalOptions_LastToFirstOrder',
    'UIA_IsEnabledPropertyId', 'TreeScope_Children',
    'UIA_IsCustomNavigationPatternAvailablePropertyId',
    'StructureChangeType', 'StyleId_Subtitle', 'HeadingLevel_None',
    'UIA_SelectionItem_ElementSelectedEventId',
    'UIA_TableColumnHeadersPropertyId',
    'UIA_InputReachedOtherElementEventId',
    'UIA_MarginTrailingAttributeId', 'UIA_DragDropEffectsPropertyId',
    'UIA_LandmarkTypePropertyId', 'UIA_LineSpacingAttributeId',
    'UIA_LegacyIAccessibleChildIdPropertyId',
    'UIA_AcceleratorKeyPropertyId', 'IUIAutomationGridPattern',
    'TreeScope_Ancestors', 'UIA_TreeItemControlTypeId',
    'ToggleState_On', 'UIA_Text_TextSelectionChangedEventId',
    'ZoomUnit_SmallDecrement',
    'UIA_IsValuePatternAvailablePropertyId', 'StyleId_Heading7',
    'IUIAutomationCustomNavigationPattern', 'UIA_InvokePatternId',
    'NotificationKind_ItemRemoved', 'IUIAutomationElement',
    'UIA_Selection_InvalidatedEventId', 'UIA_MultipleViewPatternId',
    'IUIAutomationProxyFactory', 'UIA_DockPatternId',
    'AnnotationType_Header', 'ExpandCollapseState_Collapsed',
    'AnnotationType_AdvancedProofingIssue', 'StyleId_Heading5',
    'NotificationKind_Other', 'UIA_FlowsToPropertyId',
    'UIA_RangeValuePatternId',
    'UIA_IsVirtualizedItemPatternAvailablePropertyId',
    'TextPatternRangeEndpoint', 'IUIAutomationInvokePattern',
    'AutomationElementMode_None', 'IUIAutomationAndCondition',
    'UIA_SystemAlertEventId', 'IUIAutomationFocusChangedEventHandler',
    'IUIAutomationItemContainerPattern', 'UIA_GridItemPatternId',
    'UIA_SpreadsheetItemAnnotationObjectsPropertyId',
    'UIA_FullDescriptionPropertyId',
    'UIA_IsSelectionItemPatternAvailablePropertyId',
    'SynchronizedInputType_KeyDown', 'UIA_ItemTypePropertyId',
    'UIA_RangeValueMinimumPropertyId',
    'UIA_DropTarget_DroppedEventId', 'AnnotationType_Footnote',
    'NotificationProcessing', 'UIA_LegacyIAccessibleStatePropertyId',
    'UIA_FillTypePropertyId',
    'UIA_SelectionCanSelectMultiplePropertyId',
    'UIA_UnderlineStyleAttributeId',
    'UIA_ScrollVerticallyScrollablePropertyId',
    'UIA_RadioButtonControlTypeId',
    'AnnotationType_DataValidationError',
    'UIA_IsInvokePatternAvailablePropertyId',
    'UIA_IsOffscreenPropertyId', 'UIA_GridItemColumnSpanPropertyId',
    'IUIAutomationVirtualizedItemPattern', 'UIA_AnnotationPatternId',
    'WindowVisualState_Maximized', 'UIA_TabControlTypeId',
    'IUIAutomationOrCondition', 'UIA_ButtonControlTypeId',
    'OrientationType', 'UIA_LocalizedLandmarkTypePropertyId',
    'UIA_BulletStyleAttributeId', 'UIA_MarginTopAttributeId',
    'UIA_Window_WindowOpenedEventId', 'UIA_AnimationStyleAttributeId',
    'UIA_MultipleViewCurrentViewPropertyId',
    'AnnotationType_UnsyncedChange', 'UIA_CheckBoxControlTypeId',
    'RowOrColumnMajor_ColumnMajor', 'UIA_ClassNamePropertyId',
    'UIA_GridColumnCountPropertyId'
]

