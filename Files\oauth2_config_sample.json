{"_comment": "OAuth2 Configuration for Microsoft 365 SMTP Authentication", "_instructions": ["1. Register your application in Azure AD (https://portal.azure.com)", "2. Go to Azure Active Directory > App registrations > New registration", "3. Set 'Supported account types' to 'Accounts in this organizational directory only'", "4. After registration, note the 'Application (client) ID' and 'Directory (tenant) ID'", "5. Go to 'Certificates & secrets' and create a new client secret", "6. Go to 'API permissions' and add 'SMTP.Send' application permission for Office 365 Exchange Online", "7. Grant admin consent for the permissions", "8. Fill in the values below and rename this file to 'oauth2_config.json'", "9. Alternatively, set environment variables: MICROSOFT_CLIENT_ID, MICROSOFT_CLIENT_SECRET, MICROSOFT_TENANT_ID"], "client_id": "your-application-client-id-here", "client_secret": "your-client-secret-here", "tenant_id": "your-tenant-id-here"}