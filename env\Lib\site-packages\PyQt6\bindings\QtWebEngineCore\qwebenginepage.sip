// qwebenginepage.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LIC<PERSON><PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEnginePage : public QObject
{
%TypeHeaderCode
#include <qwebenginepage.h>
%End

public:
    enum WebAction
    {
        NoWebAction,
        Back,
        Forward,
        Stop,
        Reload,
        Cut,
        Copy,
        Paste,
        Undo,
        Redo,
        SelectAll,
        ReloadAndBypassCache,
        PasteAndMatchStyle,
        OpenLinkInThisWindow,
        OpenLinkInNewWindow,
        OpenLinkInNewTab,
        CopyLinkToClipboard,
        DownloadLinkToDisk,
        CopyImageToClipboard,
        CopyImageUrlToClipboard,
        DownloadImageToDisk,
        CopyMediaUrlToClipboard,
        ToggleMediaControls,
        ToggleMediaLoop,
        ToggleMediaPlayPause,
        ToggleMediaMute,
        DownloadMediaToDisk,
        InspectElement,
        ExitFullScreen,
        RequestClose,
        Unselect,
        SavePage,
        OpenLinkInNewBackgroundTab,
        ViewSource,
        ToggleBold,
        ToggleItalic,
        ToggleUnderline,
        ToggleStrikethrough,
        AlignLeft,
        AlignCenter,
        AlignRight,
        AlignJustified,
        Indent,
        Outdent,
        InsertOrderedList,
        InsertUnorderedList,
%If (QtWebEngine_6_6_0 -)
        ChangeTextDirectionLTR,
%End
%If (QtWebEngine_6_6_0 -)
        ChangeTextDirectionRTL,
%End
    };

    enum FindFlag /BaseType=Flag/
    {
        FindBackward,
        FindCaseSensitively,
    };

    typedef QFlags<QWebEnginePage::FindFlag> FindFlags;

    enum WebWindowType
    {
        WebBrowserWindow,
        WebBrowserTab,
        WebDialog,
        WebBrowserBackgroundTab,
    };

    enum PermissionPolicy
    {
        PermissionUnknown,
        PermissionGrantedByUser,
        PermissionDeniedByUser,
    };

    enum Feature
    {
        Notifications,
        Geolocation,
        MediaAudioCapture,
        MediaVideoCapture,
        MediaAudioVideoCapture,
        MouseLock,
        DesktopVideoCapture,
        DesktopAudioVideoCapture,
%If (QtWebEngine_6_8_0 -)
        ClipboardReadWrite,
%End
%If (QtWebEngine_6_8_0 -)
        LocalFontsAccess,
%End
    };

    enum FileSelectionMode
    {
        FileSelectOpen,
        FileSelectOpenMultiple,
        FileSelectUploadFolder,
%If (QtWebEngine_6_3_0 -)
        FileSelectSave,
%End
    };

    enum JavaScriptConsoleMessageLevel
    {
        InfoMessageLevel,
        WarningMessageLevel,
        ErrorMessageLevel,
    };

    QWebEnginePage(QWebEngineProfile *profile, QObject *parent /TransferThis/ = 0);
    explicit QWebEnginePage(QObject *parent /TransferThis/ = 0);
    virtual ~QWebEnginePage();
    QWebEngineHistory *history() const;
    bool hasSelection() const;
    QString selectedText() const;
    QAction *action(QWebEnginePage::WebAction action) const;
    virtual void triggerAction(QWebEnginePage::WebAction action, bool checked = false);
    virtual bool event(QEvent *);
    void setFeaturePermission(const QUrl &securityOrigin, QWebEnginePage::Feature feature, QWebEnginePage::PermissionPolicy policy);
    void load(const QUrl &url);
    void load(const QWebEngineHttpRequest &request);
    void setHtml(const QString &html, const QUrl &baseUrl = QUrl());
    void setContent(const QByteArray &data, const QString &mimeType = QString(), const QUrl &baseUrl = QUrl());
    QString title() const;
    void setUrl(const QUrl &url);
    QUrl url() const;
    QUrl requestedUrl() const;
    QUrl iconUrl() const;
    qreal zoomFactor() const;
    void setZoomFactor(qreal factor);
    QWebEngineSettings *settings() const;

signals:
    void loadStarted();
    void loadProgress(int progress);
    void loadFinished(bool ok);
    void linkHovered(const QString &url);
    void selectionChanged();
    void geometryChangeRequested(const QRect &geom);
    void windowCloseRequested();
    void featurePermissionRequested(const QUrl &securityOrigin, QWebEnginePage::Feature feature);
    void featurePermissionRequestCanceled(const QUrl &securityOrigin, QWebEnginePage::Feature feature);
    void authenticationRequired(const QUrl &requestUrl, QAuthenticator *authenticator);
    void proxyAuthenticationRequired(const QUrl &requestUrl, QAuthenticator *authenticator, const QString &proxyHost);
    void titleChanged(const QString &title);
    void urlChanged(const QUrl &url);
    void iconUrlChanged(const QUrl &url);

protected:
    virtual QWebEnginePage *createWindow(QWebEnginePage::WebWindowType type);
    virtual QStringList chooseFiles(QWebEnginePage::FileSelectionMode mode, const QStringList &oldFiles, const QStringList &acceptedMimeTypes);
    virtual void javaScriptAlert(const QUrl &securityOrigin, const QString &msg);
    virtual bool javaScriptConfirm(const QUrl &securityOrigin, const QString &msg);
    virtual bool javaScriptPrompt(const QUrl &securityOrigin, const QString &msg, const QString &defaultValue, QString *result /Out/);
    virtual void javaScriptConsoleMessage(QWebEnginePage::JavaScriptConsoleMessageLevel level, const QString &message, int lineNumber, const QString &sourceID);

public:
    enum NavigationType
    {
        NavigationTypeLinkClicked,
        NavigationTypeTyped,
        NavigationTypeFormSubmitted,
        NavigationTypeBackForward,
        NavigationTypeReload,
        NavigationTypeRedirect,
        NavigationTypeOther,
    };

    QWebEngineProfile *profile() const;
    QWebEngineScriptCollection &scripts();
%If (PyQt_WebChannel)
    QWebChannel *webChannel() const;
%End

protected:
    virtual bool acceptNavigationRequest(const QUrl &url, QWebEnginePage::NavigationType type, bool isMainFrame);

public:
    enum RenderProcessTerminationStatus
    {
        NormalTerminationStatus,
        AbnormalTerminationStatus,
        CrashedTerminationStatus,
        KilledTerminationStatus,
    };

    QColor backgroundColor() const;
    void setBackgroundColor(const QColor &color);

signals:
    void fullScreenRequested(QWebEngineFullScreenRequest fullScreenRequest);
    void renderProcessTerminated(QWebEnginePage::RenderProcessTerminationStatus terminationStatus /ScopesStripped=1/, int exitCode);

public:
    QIcon icon() const;
    QPointF scrollPosition() const;
    QSizeF contentsSize() const;
    bool isAudioMuted() const;
    void setAudioMuted(bool muted);
    bool recentlyAudible() const;

signals:
    void iconChanged(const QIcon &icon);
    void scrollPositionChanged(const QPointF &position);
    void contentsSizeChanged(const QSizeF &size);
    void audioMutedChanged(bool muted);
    void recentlyAudibleChanged(bool recentlyAudible);
    void pdfPrintingFinished(const QString &filePath, bool success);

public:
    void replaceMisspelledWord(const QString &replacement);
    void save(const QString &filePath, QWebEngineDownloadRequest::SavePageFormat format = QWebEngineDownloadRequest::MimeHtmlSaveFormat) const /ReleaseGIL/;
    void download(const QUrl &url, const QString &filename = QString());
    void setInspectedPage(QWebEnginePage *page);
    QWebEnginePage *inspectedPage() const;
    void setDevToolsPage(QWebEnginePage *page);
    QWebEnginePage *devToolsPage() const;

signals:
    void quotaRequested(QWebEngineQuotaRequest quotaRequest);
    void registerProtocolHandlerRequested(QWebEngineRegisterProtocolHandlerRequest request);
%If (PyQt_SSL)
    void selectClientCertificate(QWebEngineClientCertificateSelection clientCertSelection);
%End
    void printRequested();

public:
    void setUrlRequestInterceptor(QWebEngineUrlRequestInterceptor *interceptor);

    enum class LifecycleState
    {
        Active,
        Frozen,
        Discarded,
    };

    QWebEnginePage::LifecycleState lifecycleState() const;
    void setLifecycleState(QWebEnginePage::LifecycleState state);
    QWebEnginePage::LifecycleState recommendedState() const;
    bool isVisible() const;
    void setVisible(bool visible);

signals:
    void visibleChanged(bool visible);
    void lifecycleStateChanged(QWebEnginePage::LifecycleState state);
    void recommendedStateChanged(QWebEnginePage::LifecycleState state);
    void findTextFinished(const QWebEngineFindTextResult &result);

public:
    qint64 renderProcessPid() const;

signals:
    void renderProcessPidChanged(qint64 pid);

public:
    void findText(const QString &subString, QWebEnginePage::FindFlags options = {}, SIP_PYCALLABLE resultCallback /AllowNone,TypeHint="Callable[[bool], None]"/ = 0);
%MethodCode
        // Make sure any callable doesn't get garbage collected until it is invoked.
        Py_XINCREF(a2);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->findText(*a0, *a1, [a2](const QWebEngineFindTextResult &arg) {
            if (a2)
            {
                SIP_BLOCK_THREADS
                
                PyObject *res;
        
                res = sipCallMethod(NULL, a2, "N", new QWebEngineFindTextResult(arg), sipType_QWebEngineFindTextResult, NULL);
        
                Py_DECREF(a2);
        
                if (!res)
                    pyqt6_qtwebenginecore_err_print();
                else
                    Py_DECREF(res);
        
                SIP_UNBLOCK_THREADS
            }
        });
        
        Py_END_ALLOW_THREADS
%End

    bool isLoading() const;
    void toHtml(SIP_PYCALLABLE resultCallback /TypeHint="Callable[[QString], None]"/) const;
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->toHtml([a0](const QString &arg) {
            SIP_BLOCK_THREADS
        
            PyObject *res;
        
            res = sipCallMethod(NULL, a0, "N", new QString(arg), sipType_QString, NULL);
        
            Py_DECREF(a0);
        
            if (!res)
                pyqt6_qtwebenginecore_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

    void toPlainText(SIP_PYCALLABLE resultCallback /TypeHint="Callable[[QString], None]"/) const;
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->toPlainText([a0](const QString &arg) {
            SIP_BLOCK_THREADS
        
            PyObject *res;
        
            res = sipCallMethod(NULL, a0, "N", new QString(arg), sipType_QString, NULL);
        
            Py_DECREF(a0);
        
            if (!res)
                pyqt6_qtwebenginecore_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

    void runJavaScript(const QString &scriptSource, quint32 worldId = 0, SIP_PYCALLABLE resultCallback /AllowNone,TypeHint="Callable[[Any], None]"/ = 0);
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_XINCREF(a2);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->runJavaScript(*a0, a1, [a2](const QVariant &arg) {
            if (a2)
            {
                SIP_BLOCK_THREADS
        
                PyObject *res;
        
                res = sipCallMethod(NULL, a2, "N", new QVariant(arg), sipType_QVariant, NULL);
        
                Py_DECREF(a2);
        
                if (!res)
                    pyqt6_qtwebenginecore_err_print();
                else
                    Py_DECREF(res);
        
                SIP_UNBLOCK_THREADS
            }
        });
        
        Py_END_ALLOW_THREADS
%End

    void runJavaScript(const QString &scriptSource, SIP_PYCALLABLE resultCallback /TypeHint="Callable[[Any], None]"/);
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a1);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->runJavaScript(*a0, [a1](const QVariant &arg) {
            SIP_BLOCK_THREADS
        
            PyObject *res;
        
            res = sipCallMethod(NULL, a1, "N", new QVariant(arg), sipType_QVariant, NULL);
        
            Py_DECREF(a1);
        
            if (!res)
                pyqt6_qtwebenginecore_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

%If (PyQt_WebChannel)
    void setWebChannel(QWebChannel *, quint32 worldId = 0);
%End
    void printToPdf(const QString &filePath, const QPageLayout &pageLayout = QPageLayout(QPageSize(QPageSize::A4), QPageLayout::Portrait, QMarginsF()), const QPageRanges &ranges = {});
    void printToPdf(SIP_PYCALLABLE resultCallback /TypeHint="Callable[[QByteArray], None]"/, const QPageLayout &pageLayout = QPageLayout(QPageSize(QPageSize::A4), QPageLayout::Portrait, QMarginsF()), const QPageRanges &ranges = {});
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->printToPdf([a0](const QByteArray &arg) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a0, "N", new QByteArray(arg), sipType_QByteArray, NULL);
        
            Py_DECREF(a0);
        
            if (!res)
                pyqt6_qtwebenginecore_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        }, *a1, *a2);
        
        Py_END_ALLOW_THREADS
%End

signals:
    void loadingChanged(const QWebEngineLoadingInfo &loadingInfo);
    void certificateError(const QWebEngineCertificateError &certificateError);
    void navigationRequested(QWebEngineNavigationRequest &request);
    void newWindowRequested(QWebEngineNewWindowRequest &request);
%If (QtWebEngine_6_4_0 -)
    void fileSystemAccessRequested(QWebEngineFileSystemAccessRequest request);
%End

public:
%If (QtWebEngine_6_6_0 -)
    QString devToolsId() const;
%End

signals:
%If (QtWebEngine_6_7_0 -)
    void desktopMediaRequested(const QWebEngineDesktopMediaRequest &request);
%End
%If (QtWebEngine_6_7_0 -)
    void webAuthUxRequested(QWebEngineWebAuthUxRequest *request);
%End

public:
%If (QtWebEngine_6_8_0 -)
    QWebEngineFrame mainFrame();
%End
%If (QtWebEngine_6_8_0 -)
    std::optional<QWebEngineFrame> findFrameByName(QAnyStringView name);
%End

signals:
%If (QtWebEngine_6_8_0 -)
    void permissionRequested(QWebEnginePermission permissionRequest);
%End
%If (QtWebEngine_6_8_0 -)
    void zoomFactorChanged(qreal factor);
%End
%If (QtWebEngine_6_8_0 -)
    void printRequestedByFrame(QWebEngineFrame frame);
%End
};
