#!/usr/bin/env python3
"""
Test script for OAuth2 SMTP authentication
This script tests the OAuth2 implementation without sending actual emails
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_oauth2_handler():
    """Test the OAuth2 handler functionality"""
    print("🧪 Testing OAuth2 Handler...")
    
    try:
        from smtp import OAuth2SMTPAuth
        
        # Create OAuth2 handler
        oauth2_handler = OAuth2SMTPAuth()
        
        # Test domain detection
        test_emails = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        print("\n📧 Testing domain detection:")
        for email in test_emails:
            is_ms = oauth2_handler.is_microsoft_domain(email)
            print(f"   {email}: {'Microsoft domain' if is_ms else 'Non-Microsoft domain'}")
        
        # Test configuration loading
        print("\n⚙️ Testing configuration:")
        oauth2_handler.configure_oauth2()
        
        if oauth2_handler.client_id:
            print(f"   ✅ Client ID configured: {oauth2_handler.client_id[:8]}...")
        else:
            print("   ⚠️ Client ID not configured")
            
        if oauth2_handler.tenant_id:
            print(f"   ✅ Tenant ID configured: {oauth2_handler.tenant_id[:8]}...")
        else:
            print("   ⚠️ Tenant ID not configured")
            
        if oauth2_handler.authority:
            print(f"   ✅ Authority configured: {oauth2_handler.authority}")
        else:
            print("   ⚠️ Authority not configured")
        
        return True
        
    except Exception as e:
        print(f"   ❌ OAuth2 handler test failed: {e}")
        return False

def test_msal_availability():
    """Test if MSAL is available"""
    print("\n📚 Testing MSAL availability...")
    
    try:
        import msal
        print("   ✅ MSAL library is available")
        print(f"   Version: {msal.__version__}")
        return True
    except ImportError:
        print("   ❌ MSAL library not available")
        print("   💡 Install with: pip install msal")
        return False

def test_config_file():
    """Test configuration file"""
    print("\n📄 Testing configuration file...")
    
    config_file = "oauth2_config.json"
    if os.path.exists(config_file):
        print(f"   ✅ Configuration file exists: {config_file}")
        try:
            import json
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            required_keys = ['client_id', 'client_secret', 'tenant_id']
            for key in required_keys:
                if key in config and config[key]:
                    print(f"   ✅ {key}: configured")
                else:
                    print(f"   ❌ {key}: missing or empty")
            return True
        except Exception as e:
            print(f"   ❌ Error reading config file: {e}")
            return False
    else:
        print(f"   ⚠️ Configuration file not found: {config_file}")
        print("   💡 Copy oauth2_config_sample.json to oauth2_config.json and fill in your credentials")
        return False

def test_environment_variables():
    """Test environment variables"""
    print("\n🌍 Testing environment variables...")
    
    env_vars = [
        'MICROSOFT_CLIENT_ID',
        'MICROSOFT_CLIENT_SECRET', 
        'MICROSOFT_TENANT_ID'
    ]
    
    found_vars = 0
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: configured")
            found_vars += 1
        else:
            print(f"   ⚠️ {var}: not set")
    
    if found_vars == 0:
        print("   💡 No environment variables set. Using config file or manual configuration.")
    elif found_vars < 3:
        print("   ⚠️ Some environment variables missing. All three are required for OAuth2.")
    else:
        print("   ✅ All environment variables configured")
    
    return found_vars == 3

def test_token_acquisition():
    """Test OAuth2 token acquisition (if configured)"""
    print("\n🔑 Testing OAuth2 token acquisition...")
    
    try:
        from smtp import oauth2_handler
        
        # Configure OAuth2
        oauth2_handler.configure_oauth2()
        
        # Check if properly configured
        if not all([oauth2_handler.client_id, oauth2_handler.client_secret, oauth2_handler.tenant_id]):
            print("   ⚠️ OAuth2 not fully configured, skipping token test")
            return False
        
        # Try to get access token
        access_token = oauth2_handler.get_access_token()
        
        if access_token:
            print(f"   ✅ Access token acquired successfully!")
            print(f"   Token length: {len(access_token)} characters")
            
            # Test XOAUTH2 string creation
            test_email = "<EMAIL>"
            xoauth2_string = oauth2_handler.create_xoauth2_string(test_email, access_token)
            print(f"   ✅ XOAUTH2 string created (length: {len(xoauth2_string)} characters)")
            
            return True
        else:
            print("   ❌ Failed to acquire access token")
            return False
            
    except Exception as e:
        print(f"   ❌ Token acquisition test failed: {e}")
        if "not properly configured" in str(e):
            print("   💡 Configure OAuth2 credentials first")
        elif "MSAL library not available" in str(e):
            print("   💡 Install MSAL with: pip install msal")
        return False

def main():
    """Main test function"""
    print("🚀 OAuth2 SMTP Authentication Test Suite")
    print("=" * 60)
    
    tests = [
        ("MSAL Library", test_msal_availability),
        ("OAuth2 Handler", test_oauth2_handler),
        ("Configuration File", test_config_file),
        ("Environment Variables", test_environment_variables),
        ("Token Acquisition", test_token_acquisition)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! OAuth2 authentication is ready to use.")
    else:
        print("⚠️ Some tests failed. Check the configuration and setup.")
        print("💡 Run 'python Files/setup_oauth2.py' for guided setup.")

if __name__ == "__main__":
    main()
