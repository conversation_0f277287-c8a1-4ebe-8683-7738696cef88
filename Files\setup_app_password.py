#!/usr/bin/env python3
"""
App Password Setup Guide - Simpler alternative to OAuth2
"""

def show_app_password_guide():
    """Show step-by-step guide for setting up app passwords"""
    print("🔐 Microsoft App Password Setup Guide")
    print("=" * 50)
    print()
    print("App passwords are a simpler alternative to OAuth2 for personal Microsoft accounts.")
    print("They work immediately without complex Azure app registration.")
    print()
    
    print("📋 Step-by-Step Instructions:")
    print()
    
    print("1. 🌐 Go to Microsoft Account Security:")
    print("   https://account.microsoft.com/security")
    print()
    
    print("2. 🔑 Sign in with your @hotmail.com/@live.com/@outlook.com account")
    print()
    
    print("3. 🛡️ Click on 'Advanced security options'")
    print()
    
    print("4. 📱 Under 'App passwords' section:")
    print("   - Click 'Create a new app password'")
    print("   - Name it: 'SMTP Email Sender' (or any name you prefer)")
    print("   - Click 'Next'")
    print()
    
    print("5. 📋 Copy the generated app password:")
    print("   - It will look like: 'abcd-efgh-ijkl-mnop'")
    print("   - This is what you'll use instead of your regular password")
    print()
    
    print("6. ✅ Use in your SMTP application:")
    print("   - Email: <EMAIL>")
    print("   - Password: the app password (not your regular password)")
    print()
    
    print("💡 Benefits of App Passwords:")
    print("   ✅ Works immediately - no complex setup")
    print("   ✅ More secure than regular passwords")
    print("   ✅ Can be revoked individually")
    print("   ✅ No OAuth2 configuration needed")
    print()
    
    print("⚠️ Important Notes:")
    print("   - App passwords only work with personal Microsoft accounts")
    print("   - You must have 2-factor authentication enabled")
    print("   - Each app password can only be used for one application")
    print()
    
    print("🔧 To use app passwords in your SMTP application:")
    print("   1. Put your @hotmail.com/@live.com email in senders.txt")
    print("   2. Use the format: email:app_password")
    print("   3. Example: <EMAIL>:abcd-efgh-ijkl-mnop")
    print()
    
    print("🆚 OAuth2 vs App Passwords:")
    print("   OAuth2: More secure, complex setup, future-proof")
    print("   App Passwords: Simple setup, works immediately, easier to manage")
    print()
    
    choice = input("Would you like to continue with OAuth2 setup or use app passwords? (oauth2/app): ").lower()
    
    if choice == 'app':
        print("\n✅ Great choice! App passwords are much simpler.")
        print("Follow the steps above to create your app password.")
        print("Then test it in your SMTP application.")
        return 'app'
    else:
        print("\n🔧 Continuing with OAuth2 setup...")
        print("You'll need to configure your Azure app registration properly.")
        return 'oauth2'

if __name__ == "__main__":
    choice = show_app_password_guide()
    
    if choice == 'app':
        print("\n📝 Next steps:")
        print("1. Create your app password following the guide above")
        print("2. Update your senders.txt file with: email:app_password")
        print("3. Test your SMTP application")
    else:
        print("\n📝 Next steps for OAuth2:")
        print("1. Update your Azure app registration:")
        print("   - Add Mobile/Desktop platform")
        print("   - Enable public client flows")
        print("   - Add redirect URI: http://localhost:8080")
        print("2. Test with: python test_browser_oauth2.py")
