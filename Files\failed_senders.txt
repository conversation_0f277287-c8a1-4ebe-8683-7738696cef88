# Failed senders log
# Format: Timestamp | Sender | Error Message

2025-06-03 19:58:41 | leah.rich<PERSON><EMAIL> | Send failed: Connection unexpectedly closed: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-03 22:06:58 | <EMAIL> | Connection unexpectedly closed: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-03 22:07:06 | <EMAIL> | (535, b'Authentication credentials invalid')
2025-06-03 22:18:57 | <EMAIL> | Server not connected
2025-06-03 22:19:47 | <EMAIL> | Send failed: Connection unexpectedly closed: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-14 20:35:20 | <EMAIL> | Health check: Both OAuth2 and app password authentication failed. OAuth2 error: MSAL library not available. Install with: pip install msal. App password error: (535, b'5.7.139 Authentication unsuccessful, basic authentication is disabled. [MA3P292CA0015.ESPP292.PROD.OUTLOOK.COM 2025-06-14T19:35:20.210Z 08DDAB623C19D3FE]')
2025-06-14 20:35:52 | <EMAIL> | Health check: Both OAuth2 and app password authentication failed. OAuth2 error: MSAL library not available. Install with: pip install msal. App password error: (535, b'5.7.139 Authentication unsuccessful, basic authentication is disabled. [MA2P292CA0004.ESPP292.PROD.OUTLOOK.COM 2025-06-14T19:35:52.474Z 08DDAAFC8EBD2F82]')
2025-06-14 21:28:48 | <EMAIL> | Health check: Both OAuth2 and app password authentication failed. OAuth2 error: OAuth2 authentication failed: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')). App password error: Connection unexpectedly closed
