// qwebenginecontextmenurequest.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineContextMenuRequest : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebenginecontextmenurequest.h>
%End

public:
    enum MediaType
    {
        MediaTypeNone,
        MediaTypeImage,
        MediaTypeVideo,
        MediaTypeAudio,
        MediaTypeCanvas,
        MediaTypeFile,
        MediaTypePlugin,
    };

    enum MediaFlag /BaseType=Flag/
    {
        MediaInError,
        MediaPaused,
        MediaMuted,
        MediaLoop,
        MediaCanSave,
        MediaHasAudio,
        MediaCanToggleControls,
        MediaControls,
        MediaCanPrint,
        MediaCanRotate,
    };

    typedef QFlags<QWebEngineContextMenuRequest::MediaFlag> MediaFlags;

    enum EditFlag /BaseType=Flag/
    {
        CanUndo,
        CanRedo,
        CanCut,
        CanCopy,
        CanPaste,
        CanDelete,
        CanSelectAll,
        CanTranslate,
        CanEditRichly,
    };

    typedef QFlags<QWebEngineContextMenuRequest::EditFlag> EditFlags;
    virtual ~QWebEngineContextMenuRequest();
    QPoint position() const;
    QString selectedText() const;
    QString linkText() const;
    QUrl linkUrl() const;
    QUrl mediaUrl() const;
    QWebEngineContextMenuRequest::MediaType mediaType() const;
    bool isContentEditable() const;
    QString misspelledWord() const;
    QStringList spellCheckerSuggestions() const;
    bool isAccepted() const;
    void setAccepted(bool accepted);
    QWebEngineContextMenuRequest::MediaFlags mediaFlags() const;
    QWebEngineContextMenuRequest::EditFlags editFlags() const;
};
