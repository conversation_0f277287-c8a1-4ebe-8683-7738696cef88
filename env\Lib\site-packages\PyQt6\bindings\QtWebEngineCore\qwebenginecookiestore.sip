// qwebenginecookiestore.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineCookieStore : public QObject
{
%TypeHeaderCode
#include <qwebenginecookiestore.h>
%End

public:
    virtual ~QWebEngineCookieStore();
    void setCookie(const QNetworkCookie &cookie, const QUrl &origin = QUrl());
    void deleteCookie(const QNetworkCookie &cookie, const QUrl &origin = QUrl());
    void deleteSessionCookies();
    void deleteAllCookies();
    void loadAllCookies();

signals:
    void cookieAdded(const QNetworkCookie &cookie);
    void cookieRemoved(const QNetworkCookie &cookie);

private:
    explicit QWebEngineCookieStore(QObject *parent = 0);

public:
    struct FilterRequest
    {
%TypeHeaderCode
#include <qwebenginecookiestore.h>
%End

        QUrl firstPartyUrl;
        QUrl origin;
        bool thirdParty;
    };

    void setCookieFilter(SIP_PYCALLABLE filterCallback /AllowNone,KeepReference,TypeHint="Callable[[FilterRequest], bool]"/ = 0);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->setCookieFilter([a0](const QWebEngineCookieStore::FilterRequest &arg) {
            int res_int = 1;
            
            if (a0)
            {
                SIP_BLOCK_THREADS
            
                PyObject *res;
        
                res = sipCallMethod(NULL, a0, "N",
                        new QWebEngineCookieStore::FilterRequest(arg),
                        sipType_QWebEngineCookieStore_FilterRequest, NULL);
            
                if (!res)
                {
                    res_int = -1;
                }
                else
                {
                    res_int = sipConvertToBool(res);
                
                    Py_DECREF(res);
                }
        
                if (res_int < 0)
                {
                    pyqt6_qtwebenginecore_err_print();
                    res_int = 0;
                }
        
                SIP_UNBLOCK_THREADS
            }
        
            return res_int;
        });
        
        Py_END_ALLOW_THREADS
%End
};

%ModuleHeaderCode
// Imports from QtCore.
typedef void (*pyqt6_qtwebenginecore_err_print_t)();
extern pyqt6_qtwebenginecore_err_print_t pyqt6_qtwebenginecore_err_print;
%End

%ModuleCode
// Imports from QtCore.
pyqt6_qtwebenginecore_err_print_t pyqt6_qtwebenginecore_err_print;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtwebenginecore_err_print = (pyqt6_qtwebenginecore_err_print_t)sipImportSymbol("pyqt6_err_print");
Q_ASSERT(pyqt6_qtwebenginecore_err_print);
%End
