// qwebenginefilesystemaccessrequest.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_6_4_0 -)

class QWebEngineFileSystemAccessRequest
{
%TypeHeaderCode
#include <qwebenginefilesystemaccessrequest.h>
%End

public:
    enum AccessFlag /BaseType=Flag/
    {
        Read,
        Write,
    };

    enum HandleType
    {
        File,
        Directory,
    };

    QWebEngineFileSystemAccessRequest(const QWebEngineFileSystemAccessRequest &other);
    ~QWebEngineFileSystemAccessRequest();
    void swap(QWebEngineFileSystemAccessRequest &other);
    typedef QFlags<QWebEngineFileSystemAccessRequest::AccessFlag> AccessFlags;
    void accept();
    void reject();
    QUrl origin() const;
    QUrl filePath() const;
    QWebEngineFileSystemAccessRequest::HandleType handleType() const;
    QWebEngineFileSystemAccessRequest::AccessFlags accessFlags() const;
};

%End
%If (QtWebEngine_6_4_0 -)
bool operator==(const QWebEngineFileSystemAccessRequest &lhs, const QWebEngineFileSystemAccessRequest &rhs);
%End
%If (QtWebEngine_6_4_0 -)
bool operator!=(const QWebEngineFileSystemAccessRequest &lhs, const QWebEngineFileSystemAccessRequest &rhs);
%End
