// qwebenginehistory.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineHistoryItem
{
%TypeHeaderCode
#include <qwebenginehistory.h>
%End

public:
    QWebEngineHistoryItem(const QWebEngineHistoryItem &other);
    ~QWebEngineHistoryItem();
    QUrl originalUrl() const;
    QUrl url() const;
    QString title() const;
    QDateTime lastVisited() const;
    QUrl iconUrl() const;
    bool isValid() const;
    void swap(QWebEngineHistoryItem &other /Constrained/);
};

QDataStream &operator<<(QDataStream &, const QWebEngineHistory &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QWebEngineHistory & /Constrained/) /ReleaseGIL/;

class QWebEngineHistoryModel : public QAbstractListModel /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebenginehistory.h>
%End

public:
    enum Roles
    {
        UrlRole,
        TitleRole,
        OffsetRole,
        IconUrlRole,
    };

    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const;
    virtual QHash<int, QByteArray> roleNames() const;
    void reset();

private:
    virtual ~QWebEngineHistoryModel();
};

class QWebEngineHistory : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebenginehistory.h>
%End

public:
    void clear();
    QList<QWebEngineHistoryItem> items() const;
    QList<QWebEngineHistoryItem> backItems(int maxItems) const;
    QList<QWebEngineHistoryItem> forwardItems(int maxItems) const;
    bool canGoBack() const;
    bool canGoForward() const;
    void back();
    void forward();
    void goToItem(const QWebEngineHistoryItem &item);
    QWebEngineHistoryItem backItem() const;
    QWebEngineHistoryItem currentItem() const;
    QWebEngineHistoryItem forwardItem() const;
    QWebEngineHistoryItem itemAt(int i) const;
    int currentItemIndex() const;
    int count() const /__len__/;
    QWebEngineHistoryModel *itemsModel() const;
    QWebEngineHistoryModel *backItemsModel() const;
    QWebEngineHistoryModel *forwardItemsModel() const;

private:
    virtual ~QWebEngineHistory();
};
