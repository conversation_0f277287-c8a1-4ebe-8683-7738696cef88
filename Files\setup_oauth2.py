#!/usr/bin/env python3
"""
Setup script for OAuth2 authentication with Microsoft 365
This script helps install dependencies and configure OAuth2 for SMTP authentication
"""

import subprocess
import sys
import os
import json

def install_msal():
    """Install the Microsoft Authentication Library (MSAL) for Python"""
    print("Installing Microsoft Authentication Library (MSAL)...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "msal"])
        print("✅ MSAL installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install MSAL: {e}")
        return False

def check_msal_installation():
    """Check if MSAL is already installed"""
    try:
        import msal
        print("✅ MSAL is already installed")
        return True
    except ImportError:
        print("⚠️ MSAL is not installed")
        return False

def create_config_file():
    """Create OAuth2 configuration file"""
    config_file = "oauth2_config.json"
    
    if os.path.exists(config_file):
        print(f"⚠️ Configuration file {config_file} already exists")
        response = input("Do you want to overwrite it? (y/N): ").strip().lower()
        if response != 'y':
            print("Configuration file creation skipped")
            return False
    
    print("\n📝 Creating OAuth2 configuration file...")
    print("You'll need to register an application in Azure AD first.")
    print("Visit: https://portal.azure.com > Azure Active Directory > App registrations")
    
    client_id = input("\nEnter your Application (client) ID: ").strip()
    client_secret = input("Enter your Client Secret: ").strip()
    tenant_id = input("Enter your Directory (tenant) ID: ").strip()
    
    if not all([client_id, client_secret, tenant_id]):
        print("❌ All fields are required")
        return False
    
    config = {
        "client_id": client_id,
        "client_secret": client_secret,
        "tenant_id": tenant_id
    }
    
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=4)
        print(f"✅ Configuration file created: {config_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to create configuration file: {e}")
        return False

def test_oauth2_connection():
    """Test OAuth2 connection"""
    print("\n🧪 Testing OAuth2 connection...")
    
    try:
        # Import the OAuth2 handler from smtp.py
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from smtp import OAuth2SMTPAuth
        
        oauth2_handler = OAuth2SMTPAuth()
        oauth2_handler.configure_oauth2()
        
        # Try to get an access token
        access_token = oauth2_handler.get_access_token()
        
        if access_token:
            print("✅ OAuth2 authentication test successful!")
            print(f"Access token obtained (length: {len(access_token)} characters)")
            return True
        else:
            print("❌ Failed to obtain access token")
            return False
            
    except Exception as e:
        print(f"❌ OAuth2 test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 OAuth2 Setup for Microsoft 365 SMTP Authentication")
    print("=" * 60)
    
    # Check and install MSAL
    if not check_msal_installation():
        if not install_msal():
            print("❌ Setup failed: Could not install MSAL")
            return False
    
    # Create configuration file
    print("\n" + "=" * 60)
    response = input("Do you want to create/update OAuth2 configuration? (Y/n): ").strip().lower()
    if response != 'n':
        if not create_config_file():
            print("⚠️ Configuration setup incomplete")
    
    # Test connection
    print("\n" + "=" * 60)
    response = input("Do you want to test OAuth2 connection? (Y/n): ").strip().lower()
    if response != 'n':
        test_oauth2_connection()
    
    print("\n" + "=" * 60)
    print("📋 Setup Summary:")
    print("1. ✅ MSAL library installed")
    print("2. 📝 Configuration file created (if requested)")
    print("3. 🧪 Connection tested (if requested)")
    print("\n💡 Next steps:")
    print("- Ensure your Azure AD application has 'SMTP.Send' permissions")
    print("- Grant admin consent for the permissions")
    print("- Your SMTP authentication will now use OAuth2 for Microsoft domains")
    print("- Basic authentication will still be used for other email providers")

if __name__ == "__main__":
    main()
