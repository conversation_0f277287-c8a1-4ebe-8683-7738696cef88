// qwebengineclienthints.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_6_8_0 -)

class QWebEngineClientHints : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebengineclienthints.h>
%End

public:
    virtual ~QWebEngineClientHints();
    QString arch() const;
    QString platform() const;
    QString model() const;
    bool isMobile() const;
    QString fullVersion() const;
    QString platformVersion() const;
    QString bitness() const;
    QVariantMap fullVersionList() const;
    bool isWow64() const;
    void setArch(const QString &);
    void setPlatform(const QString &);
    void setModel(const QString &);
    void setIsMobile(bool);
    void setFullVersion(const QString &);
    void setPlatformVersion(const QString &);
    void setBitness(const QString &);
    void setFullVersionList(const QVariantMap &);
    void setIsWow64(bool);
    bool isAllClientHintsEnabled();
    void setAllClientHintsEnabled(bool enabled);
    void resetAll();
};

%End
