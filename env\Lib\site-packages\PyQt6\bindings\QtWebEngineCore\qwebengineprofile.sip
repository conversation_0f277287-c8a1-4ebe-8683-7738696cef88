// qwebengineprofile.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineProfile : public QObject
{
%TypeHeaderCode
#include <qwebengineprofile.h>
%End

%TypeCode
// Needed by handwritten code.
#include <QIcon>
#include <QWebEngineNotification>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
    #if QT_VERSION >= 0x060800
        {sipName_QWebEngineClientHints, &sipType_QWebEngineClientHints, -1, 1},
    #else
        {0, 0, -1, 1},
    #endif
        {sipName_QWebEngineContextMenuRequest, &sipType_QWebEngineContextMenuRequest, -1, 2},
        {sipName_QWebEngineCookieStore, &sipType_QWebEngineCookieStore, -1, 3},
        {sipName_QWebEngineDownloadRequest, &sipType_QWebEngineDownloadRequest, -1, 4},
        {sipName_QWebEngineHistory, &sipType_QWebEngineHistory, -1, 5},
        {sipName_QWebEngineHistoryModel, &sipType_QWebEngineHistoryModel, -1, 6},
        {sipName_QWebEngineNavigationRequest, &sipType_QWebEngineNavigationRequest, -1, 7},
        {sipName_QWebEngineNewWindowRequest, &sipType_QWebEngineNewWindowRequest, -1, 8},
        {sipName_QWebEngineNotification, &sipType_QWebEngineNotification, -1, 9},
        {sipName_QWebEnginePage, &sipType_QWebEnginePage, -1, 10},
        {sipName_QWebEngineProfile, &sipType_QWebEngineProfile, -1, 11},
        {sipName_QWebEngineUrlRequestInterceptor, &sipType_QWebEngineUrlRequestInterceptor, -1, 12},
        {sipName_QWebEngineUrlRequestJob, &sipType_QWebEngineUrlRequestJob, -1, 13},
        {sipName_QWebEngineUrlSchemeHandler, &sipType_QWebEngineUrlSchemeHandler, -1, 14},
    #if QT_VERSION >= 0x060700
        {sipName_QWebEngineWebAuthUxRequest, &sipType_QWebEngineWebAuthUxRequest, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    explicit QWebEngineProfile(QObject *parent /TransferThis/ = 0);
    QWebEngineProfile(const QString &name, QObject *parent /TransferThis/ = 0);
    virtual ~QWebEngineProfile();

    enum HttpCacheType
    {
        MemoryHttpCache,
        DiskHttpCache,
        NoCache,
    };

    enum PersistentCookiesPolicy
    {
        NoPersistentCookies,
        AllowPersistentCookies,
        ForcePersistentCookies,
    };

    QString storageName() const;
    bool isOffTheRecord() const;
    QString persistentStoragePath() const;
    void setPersistentStoragePath(const QString &path);
    QString cachePath() const;
    void setCachePath(const QString &path);
    QString httpUserAgent() const;
    void setHttpUserAgent(const QString &userAgent);
    QWebEngineProfile::HttpCacheType httpCacheType() const;
    void setHttpCacheType(QWebEngineProfile::HttpCacheType);
    QWebEngineProfile::PersistentCookiesPolicy persistentCookiesPolicy() const;
    void setPersistentCookiesPolicy(QWebEngineProfile::PersistentCookiesPolicy);
    int httpCacheMaximumSize() const;
    void setHttpCacheMaximumSize(int maxSize);
    void clearAllVisitedLinks();
    void clearVisitedLinks(const QList<QUrl> &urls);
    bool visitedLinksContainsUrl(const QUrl &url) const;
    QWebEngineSettings *settings() const;
    QWebEngineScriptCollection *scripts() const;
    static QWebEngineProfile *defaultProfile() /Transfer/;

signals:
    void downloadRequested(QWebEngineDownloadRequest *download);
%If (QtWebEngine_6_7_0 -)
    void clearHttpCacheCompleted();
%End

public:
    void setHttpAcceptLanguage(const QString &httpAcceptLanguage);
    QString httpAcceptLanguage() const;
    QWebEngineCookieStore *cookieStore() /Transfer/;
    void setUrlRequestInterceptor(QWebEngineUrlRequestInterceptor *interceptor);
    const QWebEngineUrlSchemeHandler *urlSchemeHandler(const QByteArray &) const;
    void installUrlSchemeHandler(const QByteArray &scheme, QWebEngineUrlSchemeHandler *);
    void removeUrlScheme(const QByteArray &scheme);
    void removeUrlSchemeHandler(QWebEngineUrlSchemeHandler *);
    void removeAllUrlSchemeHandlers();
    void clearHttpCache();
    void setSpellCheckLanguages(const QStringList &languages);
    QStringList spellCheckLanguages() const;
    void setSpellCheckEnabled(bool enabled);
    bool isSpellCheckEnabled() const;
    QString downloadPath() const;
    void setDownloadPath(const QString &path);
    void setNotificationPresenter(SIP_PYCALLABLE /AllowNone, KeepReference, TypeHint="Callable[[QWebEngineNotification], None]"/);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->setNotificationPresenter([a0](std::unique_ptr<QWebEngineNotification> arg) {
            if (a0)
            {
                SIP_BLOCK_THREADS
                
                // We have to find the generated type structure because a name isn't
                // automatically generated as it isn't used anywhere else in the
                // module.
                PyObject *arg_obj = sipConvertFromNewType(arg.release(),
                        sipFindType("QWebEngineNotification"), NULL);
                
                if (arg_obj)
                {
                    PyObject *res = PyObject_CallFunctionObjArgs(a0, arg_obj, NULL);
        
                    Py_DECREF(arg_obj);
        
                    if (!res)
                        pyqt6_qtwebenginecore_err_print();
                    else
                        Py_DECREF(res);
                }
                else
                {
                    pyqt6_qtwebenginecore_err_print();
                }
        
                SIP_UNBLOCK_THREADS
            }
        });
        
        Py_END_ALLOW_THREADS
%End

%If (PyQt_SSL)
    QWebEngineClientCertificateStore *clientCertificateStore();
%End
    void requestIconForPageURL(const QUrl &url, int desiredSizeInPixel, SIP_PYCALLABLE iconAvailableCallback /TypeHint="Callable[[QIcon, QUrl, QUrl], None]"/) const;
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a2);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->requestIconForPageURL(*a0, a1, [a2](const QIcon &arg0, const QUrl &arg1, const QUrl &arg2) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a2, "NNN", new QIcon(arg0), sipType_QIcon, new QUrl(arg1), sipType_QUrl, new QUrl(arg2), sipType_QUrl, NULL);
        
            Py_DECREF(a2);
        
            if (!res)
                pyqt6_qtwebenginecore_err_print();
            else
                Py_DECREF(res);
        
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

    void requestIconForIconURL(const QUrl &url, int desiredSizeInPixel, SIP_PYCALLABLE iconAvailableCallback /TypeHint="Callable[[QIcon, QUrl], None]"/) const;
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a2);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->requestIconForIconURL(*a0, a1, [a2](const QIcon &arg0, const QUrl &arg1) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a2, "NN", new QIcon(arg0), sipType_QIcon, new QUrl(arg1), sipType_QUrl, NULL);
        
            Py_DECREF(a2);
        
            if (!res)
                pyqt6_qtwebenginecore_err_print();
            else
                Py_DECREF(res);
        
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

%If (QtWebEngine_6_5_0 -)
    bool isPushServiceEnabled() const;
%End
%If (QtWebEngine_6_5_0 -)
    void setPushServiceEnabled(bool enabled);
%End
%If (QtWebEngine_6_8_0 -)

    enum class PersistentPermissionsPolicy
    {
        AskEveryTime,
        StoreInMemory,
        StoreOnDisk,
    };

%End
%If (QtWebEngine_6_8_0 -)
    QWebEngineProfile::PersistentPermissionsPolicy persistentPermissionsPolicy() const;
%End
%If (QtWebEngine_6_8_0 -)
    void setPersistentPermissionsPolicy(QWebEngineProfile::PersistentPermissionsPolicy);
%End
%If (QtWebEngine_6_8_0 -)
    QWebEngineClientHints *clientHints() const /Transfer/;
%End
%If (QtWebEngine_6_8_0 -)
    QWebEnginePermission queryPermission(const QUrl &securityOrigin, QWebEnginePermission::PermissionType permissionType) const;
%End
%If (QtWebEngine_6_8_0 -)
    QList<QWebEnginePermission> listAllPermissions() const;
%End
%If (QtWebEngine_6_8_0 -)
    QList<QWebEnginePermission> listPermissionsForOrigin(const QUrl &securityOrigin) const;
%End
%If (QtWebEngine_6_8_0 -)
    QList<QWebEnginePermission> listPermissionsForPermissionType(QWebEnginePermission::PermissionType permissionType) const;
%End
};
