// qwebenginepermission.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_6_8_0 -)

class QWebEnginePermission
{
%TypeHeaderCode
#include <qwebenginepermission.h>
%End

public:
    QWebEnginePermission();
    QWebEnginePermission(const QWebEnginePermission &other);
    ~QWebEnginePermission();
    void swap(QWebEnginePermission &other);

    enum class PermissionType
    {
        Unsupported,
        MediaAudioCapture,
        MediaVideoCapture,
        MediaAudioVideoCapture,
        DesktopVideoCapture,
        DesktopAudioVideoCapture,
        MouseLock,
        Notifications,
        Geolocation,
        ClipboardReadWrite,
        LocalFontsAccess,
    };

    enum class State
    {
        Invalid,
        Ask,
        Granted,
        Denied,
    };

    QUrl origin() const;
    QWebEnginePermission::PermissionType permissionType() const;
    QWebEnginePermission::State state() const;
    bool isValid() const;
    void grant() const;
    void deny() const;
    void reset() const;
    static bool isPersistent(QWebEnginePermission::PermissionType permissionType);
};

%End
%If (QtWebEngine_6_8_0 -)
bool operator==(const QWebEnginePermission &lhs, const QWebEnginePermission &rhs);
%End
%If (QtWebEngine_6_8_0 -)
bool operator!=(const QWebEnginePermission &lhs, const QWebEnginePermission &rhs);
%End
