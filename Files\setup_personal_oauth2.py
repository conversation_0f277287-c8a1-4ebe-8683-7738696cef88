#!/usr/bin/env python3
"""
Simple setup script for OAuth2 authentication with Personal Microsoft Accounts
For @hotmail.com, @live.com, @outlook.com accounts
"""

import subprocess
import sys
import os
import json

def install_msal():
    """Install the Microsoft Authentication Library (MSAL) for Python"""
    print("Installing Microsoft Authentication Library (MSAL)...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "msal"])
        print("✅ MSAL installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install MSAL: {e}")
        return False

def check_msal_installation():
    """Check if MSAL is already installed"""
    try:
        import msal
        print("✅ MSAL is already installed")
        return True
    except ImportError:
        print("⚠️ MSAL is not installed")
        return False

def create_personal_config():
    """Create OAuth2 configuration for personal accounts"""
    config_file = "oauth2_config.json"
    
    if os.path.exists(config_file):
        print(f"⚠️ Configuration file {config_file} already exists")
        response = input("Do you want to overwrite it? (y/N): ").strip().lower()
        if response != 'y':
            print("Configuration file creation skipped")
            return False
    
    print("\n📝 Setting up OAuth2 for Personal Microsoft Accounts")
    print("=" * 60)
    print("You need to register an application in Azure AD first:")
    print("1. Go to: https://portal.azure.com")
    print("2. Navigate to: Azure Active Directory > App registrations")
    print("3. Click: New registration")
    print("4. Name: Your app name (e.g., 'Personal Email Sender')")
    print("5. Account types: 'Personal Microsoft accounts only'")
    print("6. Redirect URI: Leave blank")
    print("7. Click: Register")
    print("8. API Permissions: Add Microsoft Graph > Mail.Send (Delegated)")
    print("=" * 60)
    
    client_id = input("\nEnter your Application (client) ID: ").strip()
    
    if not client_id:
        print("❌ Client ID is required")
        return False
    
    config = {
        "account_type": "personal",
        "client_id": client_id,
        "_comment": "Personal Microsoft account configuration - no client_secret or tenant_id needed"
    }
    
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=4)
        print(f"✅ Configuration file created: {config_file}")
        print("\n📋 Configuration Summary:")
        print(f"   Account Type: Personal")
        print(f"   Client ID: {client_id}")
        print(f"   Authentication: Interactive device code flow")
        return True
    except Exception as e:
        print(f"❌ Failed to create configuration file: {e}")
        return False

def test_personal_oauth2():
    """Test OAuth2 connection for personal accounts"""
    print("\n🧪 Testing OAuth2 connection...")
    
    try:
        # Import the OAuth2 handler from smtp.py
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from smtp import OAuth2SMTPAuth
        
        oauth2_handler = OAuth2SMTPAuth()
        oauth2_handler.configure_oauth2()
        
        if not oauth2_handler.client_id:
            print("❌ OAuth2 not configured. Run configuration setup first.")
            return False
        
        print(f"✅ Configuration loaded:")
        print(f"   Account Type: {oauth2_handler.account_type}")
        print(f"   Client ID: {oauth2_handler.client_id}")
        print(f"   Authority: {oauth2_handler.authority}")
        
        # Note: We don't actually try to get a token here as it requires user interaction
        print("\n💡 Configuration looks good!")
        print("   When you send emails, you'll be prompted to authenticate interactively.")
        print("   After first authentication, tokens will be cached for future use.")
        
        return True
        
    except Exception as e:
        print(f"❌ OAuth2 test failed: {e}")
        return False

def main():
    """Main setup function for personal accounts"""
    print("🚀 OAuth2 Setup for Personal Microsoft Accounts")
    print("   (@hotmail.com, @live.com, @outlook.com)")
    print("=" * 60)
    
    # Check and install MSAL
    if not check_msal_installation():
        if not install_msal():
            print("❌ Setup failed: Could not install MSAL")
            return False
    
    # Create configuration file
    print("\n" + "=" * 60)
    if not create_personal_config():
        print("❌ Setup failed: Could not create configuration")
        return False
    
    # Test configuration
    print("\n" + "=" * 60)
    test_personal_oauth2()
    
    print("\n" + "=" * 60)
    print("🎉 Setup Complete!")
    print("\n📋 What happens next:")
    print("1. ✅ MSAL library installed")
    print("2. ✅ Personal account configuration created")
    print("3. 🔐 First email send will prompt for interactive authentication")
    print("4. 💾 Subsequent sends will use cached tokens (automatic)")
    print("\n💡 Important Notes:")
    print("- Works with @hotmail.com, @live.com, @outlook.com accounts")
    print("- No client secret or tenant ID needed for personal accounts")
    print("- Authentication is interactive (device code flow)")
    print("- Tokens are cached locally for future use")
    print("\n🚀 You're ready to send emails with OAuth2!")

if __name__ == "__main__":
    main()
