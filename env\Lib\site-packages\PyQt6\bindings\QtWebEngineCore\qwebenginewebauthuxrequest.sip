// qwebenginewebauthuxrequest.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_6_7_0 -)

class QWebEngineWebAuthUxRequest : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebenginewebauthuxrequest.h>
%End

public:
    enum class WebAuthUxState
    {
        NotStarted,
        SelectAccount,
        CollectPin,
        FinishTokenCollection,
        RequestFailed,
        Cancelled,
        Completed,
    };

    enum class PinEntryReason
    {
        Set,
        Change,
        Challenge,
    };

    enum class PinEntryError
    {
        NoError,
        InternalUvLocked,
        WrongPin,
        TooShort,
        InvalidCharacters,
        SameAsCurrentPin,
    };

    enum class RequestFailureReason
    {
        Timeout,
        KeyNotRegistered,
        KeyAlreadyRegistered,
        SoftPinBlock,
        HardPinBlock,
        AuthenticatorRemovedDuringPinEntry,
        AuthenticatorMissingResidentKeys,
        AuthenticatorMissingUserVerification,
        AuthenticatorMissingLargeBlob,
        NoCommonAlgorithms,
        StorageFull,
        UserConsentDenied,
        WinUserCancelled,
    };

    virtual ~QWebEngineWebAuthUxRequest();
    QStringList userNames() const;
    QString relyingPartyId() const;
    QWebEngineWebAuthPinRequest pinRequest() const;
    QWebEngineWebAuthUxRequest::WebAuthUxState state() const;
    QWebEngineWebAuthUxRequest::RequestFailureReason requestFailureReason() const;

signals:
    void stateChanged(QWebEngineWebAuthUxRequest::WebAuthUxState state);

public slots:
    void cancel();
    void retry();
    void setSelectedAccount(const QString &selectedAccount);
    void setPin(const QString &pin);
};

%End
%If (QtWebEngine_6_7_0 -)

struct QWebEngineWebAuthPinRequest
{
%TypeHeaderCode
#include <qwebenginewebauthuxrequest.h>
%End

    QWebEngineWebAuthUxRequest::PinEntryReason reason;
    QWebEngineWebAuthUxRequest::PinEntryError error;
    qint32 minPinLength;
    int remainingAttempts;
};

%End
