// qwebengineframe.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_6_8_0 -)

class QWebEngineFrame /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebengineframe.h>
%End

%TypeCode
#include <QVariant>
%End

public:
    bool isValid() const;
    QString name() const;
    QString htmlName() const;
    QList<QWebEngineFrame> children() const;
    QUrl url() const;
    QSizeF size() const;
    bool isMainFrame() const;
    void runJavaScript(const QString &script, quint32 worldId = 0);
    void runJavaScript(const QString &script, SIP_PYCALLABLE callback /TypeHint="Callable[[Any], None]"/);
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a1);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->runJavaScript(*a0, [a1](const QVariant &arg) {
            SIP_BLOCK_THREADS
        
            PyObject *res;
        
            res = sipCallMethod(NULL, a1, "N", new QVariant(arg), sipType_QVariant, NULL);
        
            Py_DECREF(a1);
        
            if (!res)
                pyqt6_qtwebenginecore_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

    void runJavaScript(const QString &script, quint32 worldId, SIP_PYCALLABLE callback /TypeHint="Callable[[Any], None]"/);
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a2);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->runJavaScript(*a0, a1, [a2](const QVariant &arg) {
            SIP_BLOCK_THREADS
        
            PyObject *res;
        
            res = sipCallMethod(NULL, a2, "N", new QVariant(arg), sipType_QVariant, NULL);
        
            Py_DECREF(a2);
        
            if (!res)
                pyqt6_qtwebenginecore_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

    void printToPdf(const QString &filePath);
    void printToPdf(SIP_PYCALLABLE callback /TypeHint="Callable[[QByteArray], None]"/);
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->printToPdf([a0](const QByteArray &arg) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a0, "N", new QByteArray(arg), sipType_QByteArray, NULL);
        
            Py_DECREF(a0);
        
            if (!res)
                pyqt6_qtwebenginecore_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End
};

%End
%If (QtWebEngine_6_8_0 -)
bool operator!=(const QWebEngineFrame &lhs, const QWebEngineFrame &rhs);
%End
%If (QtWebEngine_6_8_0 -)
bool operator==(const QWebEngineFrame &lhs, const QWebEngineFrame &rhs);
%End
