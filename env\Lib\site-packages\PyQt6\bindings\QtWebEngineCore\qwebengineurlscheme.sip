// qwebengineurlscheme.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineUrlScheme
{
%TypeHeaderCode
#include <qwebengineurlscheme.h>
%End

public:
    enum class Syntax
    {
        HostPortAndUserInformation,
        HostAndPort,
        Host,
        Path,
    };

    enum SpecialPort
    {
        PortUnspecified,
    };

    enum Flag /BaseType=Flag/
    {
        SecureScheme,
        LocalScheme,
        LocalAccessAllowed,
        NoAccessAllowed,
        ServiceWorkersAllowed,
        ViewSourceAllowed,
        ContentSecurityPolicyIgnored,
        CorsEnabled,
%If (QtWebEngine_6_6_0 -)
        FetchApiAllowed,
%End
    };

    typedef QFlags<QWebEngineUrlScheme::Flag> Flags;
    QWebEngineUrlScheme();
    explicit QWebEngineUrlScheme(const QByteArray &name);
    QWebEngineUrlScheme(const QWebEngineUrlScheme &that);
    ~QWebEngineUrlScheme();
    bool operator==(const QWebEngineUrlScheme &that) const;
    bool operator!=(const QWebEngineUrlScheme &that) const;
    QByteArray name() const;
    void setName(const QByteArray &newValue);
    QWebEngineUrlScheme::Syntax syntax() const;
    void setSyntax(QWebEngineUrlScheme::Syntax newValue);
    int defaultPort() const;
    void setDefaultPort(int newValue);
    QWebEngineUrlScheme::Flags flags() const;
    void setFlags(QWebEngineUrlScheme::Flags newValue);
    static void registerScheme(const QWebEngineUrlScheme &scheme);
    static QWebEngineUrlScheme schemeByName(const QByteArray &name);
};
