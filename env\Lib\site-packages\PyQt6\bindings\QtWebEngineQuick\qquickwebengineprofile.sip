// qquickwebengineprofile.sip generated by MetaSIP
//
// This file is part of the QtWebEngineQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickWebEngineProfile : public QObject
{
%TypeHeaderCode
#include <qquickwebengineprofile.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QQuickWebEngineProfile, &sipType_QQuickWebEngineProfile, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QQuickWebEngineProfile(QObject *parent /TransferThis/ = 0);
%If (QtWebEngine_6_9_0 -)
    QQuickWebEngineProfile(const QString &storageName, QObject *parent /TransferThis/ = 0);
%End
    virtual ~QQuickWebEngineProfile();

    enum HttpCacheType
    {
        MemoryHttpCache,
        DiskHttpCache,
        NoCache,
    };

    enum PersistentCookiesPolicy
    {
        NoPersistentCookies,
        AllowPersistentCookies,
        ForcePersistentCookies,
    };

    QString storageName() const;
    void setStorageName(const QString &name);
    bool isOffTheRecord() const;
    void setOffTheRecord(bool offTheRecord);
    QString persistentStoragePath() const;
    void setPersistentStoragePath(const QString &path);
    QString cachePath() const;
    void setCachePath(const QString &path);
    QString httpUserAgent() const;
    void setHttpUserAgent(const QString &userAgent);
    QQuickWebEngineProfile::HttpCacheType httpCacheType() const;
    void setHttpCacheType(QQuickWebEngineProfile::HttpCacheType);
    QQuickWebEngineProfile::PersistentCookiesPolicy persistentCookiesPolicy() const;
    void setPersistentCookiesPolicy(QQuickWebEngineProfile::PersistentCookiesPolicy);
    int httpCacheMaximumSize() const;
    void setHttpCacheMaximumSize(int maxSize);
    QString httpAcceptLanguage() const;
    void setHttpAcceptLanguage(const QString &httpAcceptLanguage);
    QWebEngineCookieStore *cookieStore() const /Transfer/;
    void setUrlRequestInterceptor(QWebEngineUrlRequestInterceptor *interceptor);
    const QWebEngineUrlSchemeHandler *urlSchemeHandler(const QByteArray &) const;
    void installUrlSchemeHandler(const QByteArray &scheme, QWebEngineUrlSchemeHandler *);
    void removeUrlScheme(const QByteArray &scheme);
    void removeUrlSchemeHandler(QWebEngineUrlSchemeHandler *);
    void removeAllUrlSchemeHandlers();
    void clearHttpCache();
    static QQuickWebEngineProfile *defaultProfile() /Transfer/;

signals:
    void storageNameChanged();
    void offTheRecordChanged();
    void persistentStoragePathChanged();
    void cachePathChanged();
    void httpUserAgentChanged();
    void httpCacheTypeChanged();
    void persistentCookiesPolicyChanged();
    void httpCacheMaximumSizeChanged();
    void httpAcceptLanguageChanged();

public:
    void setSpellCheckLanguages(const QStringList &languages);
    QStringList spellCheckLanguages() const;
    void setSpellCheckEnabled(bool enabled);
    bool isSpellCheckEnabled() const;

signals:
    void spellCheckLanguagesChanged();
    void spellCheckEnabledChanged();

public:
    QString downloadPath() const;
    void setDownloadPath(const QString &path);
%If (PyQt_SSL)
    QWebEngineClientCertificateStore *clientCertificateStore();
%End

signals:
    void downloadPathChanged();
    void presentNotification(QWebEngineNotification *notification);

public:
%If (QtWebEngine_6_5_0 -)
    bool isPushServiceEnabled() const;
%End
%If (QtWebEngine_6_5_0 -)
    void setPushServiceEnabled(bool enable);
%End

signals:
%If (QtWebEngine_6_5_0 -)
    void pushServiceEnabledChanged();
%End
%If (QtWebEngine_6_7_0 -)
    void clearHttpCacheCompleted();
%End

public:
%If (QtWebEngine_6_8_0 -)

    enum class PersistentPermissionsPolicy
    {
        AskEveryTime,
        StoreInMemory,
        StoreOnDisk,
    };

%End
%If (QtWebEngine_6_8_0 -)
    QQuickWebEngineProfile::PersistentPermissionsPolicy persistentPermissionsPolicy() const;
%End
%If (QtWebEngine_6_8_0 -)
    void setPersistentPermissionsPolicy(QQuickWebEngineProfile::PersistentPermissionsPolicy);
%End
%If (QtWebEngine_6_8_0 -)
    QWebEngineClientHints *clientHints() const /Transfer/;
%End
%If (QtWebEngine_6_8_0 -)
    QWebEnginePermission queryPermission(const QUrl &securityOrigin, QWebEnginePermission::PermissionType permissionType) const;
%End
%If (QtWebEngine_6_8_0 -)
    QList<QWebEnginePermission> listAllPermissions() const;
%End
%If (QtWebEngine_6_8_0 -)
    QList<QWebEnginePermission> listPermissionsForOrigin(const QUrl &securityOrigin) const;
%End
%If (QtWebEngine_6_8_0 -)
    QList<QWebEnginePermission> listPermissionsForPermissionType(QWebEnginePermission::PermissionType permissionType) const;
%End

signals:
%If (QtWebEngine_6_8_0 -)
    void persistentPermissionsPolicyChanged();
%End
};
