from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    CoClass, VgaColor, FONTSIZ<PERSON>, EXCEPINFO, GUID, OLE_CANCELBOOL,
    <PERSON>LE_ENABLEDEFAULTBOOL, OLE_XSIZE_HIMETRIC, BSTR, Library,
    FONTNAME, StdPicture, FONTBOLD, IFontEventsDisp, _check_version,
    IPictureDisp, IPicture, DISPPROPERTY, OLE_YSIZE_HIMETRIC,
    OLE_COLOR, IUnknown, Unchecked, OLE_YSIZE_PIXELS, DISPPARAMS,
    OLE_XPOS_PIXELS, OLE_YPOS_HIMETRIC, FONTSTRIKETHROUGH,
    <PERSON>LE_<PERSON>POS_CONTAINER, OLE_XSIZE_CONTAINER, IFontDisp, StdFont,
    FONTUNDERSCOR<PERSON>, HRESULT, Color, IEnumVARIANT, <PERSON>LE_XSIZE_PIXELS,
    VARIANT_BOOL, OLE_YPOS_CONTAINER, COMMETHOD, Default, IFont, Gray,
    _lcid, Font, Checked, typelib_path, OLE_XPOS_HIMETRIC, OLE_HANDLE,
    FONTITALIC, DISPMETHOD, dispid, OLE_YSIZE_CONTAINER, Monochrome,
    FontEvents, OLE_YPOS_PIXELS, Picture, OLE_OPTEXCLUSIVE, IDispatch
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'OLE_XSIZE_CONTAINER', 'IFontDisp', 'VgaColor', 'FONTSIZE',
    'StdFont', 'FONTUNDERSCORE', 'Color', 'OLE_TRISTATE',
    'OLE_XSIZE_PIXELS', 'OLE_CANCELBOOL', 'OLE_YPOS_CONTAINER',
    'OLE_ENABLEDEFAULTBOOL', 'OLE_XSIZE_HIMETRIC', 'Default',
    'Library', 'IFont', 'FONTNAME', 'Gray', 'StdPicture', 'Font',
    'FONTBOLD', 'IFontEventsDisp', 'IPictureDisp', 'IPicture',
    'Checked', 'typelib_path', 'OLE_YSIZE_HIMETRIC',
    'OLE_XPOS_HIMETRIC', 'OLE_HANDLE', 'OLE_COLOR', 'FONTITALIC',
    'LoadPictureConstants', 'Unchecked', 'OLE_YSIZE_PIXELS',
    'OLE_XPOS_PIXELS', 'OLE_YSIZE_CONTAINER', 'OLE_YPOS_HIMETRIC',
    'FONTSTRIKETHROUGH', 'Monochrome', 'FontEvents',
    'OLE_XPOS_CONTAINER', 'OLE_YPOS_PIXELS', 'Picture',
    'OLE_OPTEXCLUSIVE'
]

