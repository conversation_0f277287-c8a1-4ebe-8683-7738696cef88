// qwebenginesettings.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineSettings /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebenginesettings.h>
%End

public:
    enum FontFamily
    {
        StandardFont,
        FixedFont,
        SerifFont,
        SansSerifFont,
        CursiveFont,
        FantasyFont,
        PictographFont,
    };

    enum WebAttribute
    {
        AutoLoadImages,
        JavascriptEnabled,
        JavascriptCanOpenWindows,
        JavascriptCanAccessClipboard,
        LinksIncludedInFocusChain,
        LocalStorageEnabled,
        LocalContentCanAccessRemoteUrls,
        XSSAuditingEnabled,
        SpatialNavigationEnabled,
        LocalContentCanAccessFileUrls,
        HyperlinkAuditingEnabled,
        ScrollAnimatorEnabled,
        ErrorPageEnabled,
        PluginsEnabled,
        FullScreenSupportEnabled,
        ScreenCaptureEnabled,
        WebGLEnabled,
        Accelerated2dCanvasEnabled,
        AutoLoadIconsForPage,
        TouchIconsEnabled,
        FocusOnNavigationEnabled,
        PrintElementBackgrounds,
        AllowRunningInsecureContent,
        AllowGeolocationOnInsecureOrigins,
        AllowWindowActivationFromJavaScript,
        ShowScrollBars,
        PlaybackRequiresUserGesture,
        WebRTCPublicInterfacesOnly,
        JavascriptCanPaste,
        DnsPrefetchEnabled,
        PdfViewerEnabled,
%If (QtWebEngine_6_4_0 -)
        NavigateOnDropEnabled,
%End
%If (QtWebEngine_6_6_0 -)
        ReadingFromCanvasEnabled,
%End
%If (QtWebEngine_6_7_0 -)
        ForceDarkMode,
%End
%If (QtWebEngine_6_9_0 -)
        PrintHeaderAndFooter,
%End
%If (QtWebEngine_6_9_0 -)
        PreferCSSMarginsForPrinting,
%End
%If (QtWebEngine_6_9_0 -)
        TouchEventsApiEnabled,
%End
    };

    enum FontSize
    {
        MinimumFontSize,
        MinimumLogicalFontSize,
        DefaultFontSize,
        DefaultFixedFontSize,
    };

    enum UnknownUrlSchemePolicy
    {
        DisallowUnknownUrlSchemes,
        AllowUnknownUrlSchemesFromUserInteraction,
        AllowAllUnknownUrlSchemes,
    };

    ~QWebEngineSettings();
    void setFontFamily(QWebEngineSettings::FontFamily which, const QString &family);
    QString fontFamily(QWebEngineSettings::FontFamily which) const;
    void resetFontFamily(QWebEngineSettings::FontFamily which);
    void setFontSize(QWebEngineSettings::FontSize type, int size);
    int fontSize(QWebEngineSettings::FontSize type) const;
    void resetFontSize(QWebEngineSettings::FontSize type);
    void setAttribute(QWebEngineSettings::WebAttribute attr, bool on);
    bool testAttribute(QWebEngineSettings::WebAttribute attr) const;
    void resetAttribute(QWebEngineSettings::WebAttribute attr);
    void setDefaultTextEncoding(const QString &encoding);
    QString defaultTextEncoding() const;
    QWebEngineSettings::UnknownUrlSchemePolicy unknownUrlSchemePolicy() const;
    void setUnknownUrlSchemePolicy(QWebEngineSettings::UnknownUrlSchemePolicy policy);
    void resetUnknownUrlSchemePolicy();
%If (QtWebEngine_6_8_0 -)

    enum class ImageAnimationPolicy
    {
        Allow,
        AnimateOnce,
        Disallow,
    };

%End
%If (QtWebEngine_6_8_0 -)
    void setImageAnimationPolicy(QWebEngineSettings::ImageAnimationPolicy policy);
%End
%If (QtWebEngine_6_8_0 -)
    QWebEngineSettings::ImageAnimationPolicy imageAnimationPolicy() const;
%End
%If (QtWebEngine_6_8_0 -)
    void resetImageAnimationPolicy();
%End
};
