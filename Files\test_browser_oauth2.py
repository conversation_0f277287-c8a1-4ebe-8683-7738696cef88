#!/usr/bin/env python3
"""
Browser-based OAuth2 Test - Uses interactive browser authentication
"""

import msal
import json
import os
import webbrowser
from urllib.parse import urlparse, parse_qs

def test_browser_oauth2():
    """Test OAuth2 with browser-based authentication"""
    print("🔐 Browser OAuth2 Test")
    print("=" * 30)
    
    # Load config
    config_file = "oauth2_config.json"
    if not os.path.exists(config_file):
        print(f"❌ Config file not found: {config_file}")
        return False
    
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    client_id = config.get('client_id')
    if not client_id:
        print("❌ No client_id in config")
        return False
    
    print(f"✅ Client ID: {client_id[:12]}...")
    
    # Create MSAL app with redirect URI
    authority = "https://login.microsoftonline.com/consumers"
    scope = ["https://graph.microsoft.com/Mail.Send"]
    redirect_uri = "http://localhost:8080"  # Local redirect
    
    try:
        app = msal.PublicClientApplication(
            client_id,
            authority=authority,
        )
        print("✅ MSAL app created")
    except Exception as e:
        print(f"❌ Failed to create MSAL app: {e}")
        return False
    
    # Check for cached tokens first
    accounts = app.get_accounts()
    if accounts:
        print(f"✅ Found {len(accounts)} cached account(s)")
        try:
            result = app.acquire_token_silent(scope, account=accounts[0])
            if result and "access_token" in result:
                print("✅ Used cached token successfully!")
                print(f"   Access token length: {len(result['access_token'])}")
                return True
        except Exception as e:
            print(f"⚠️ Cached token failed: {e}")
    
    # Try interactive authentication
    try:
        print("\n🌐 Starting interactive browser authentication...")
        print("   This will open your default browser")
        
        # Get authorization URL
        auth_url = app.get_authorization_request_url(
            scopes=scope,
            redirect_uri=redirect_uri
        )
        
        print(f"✅ Authorization URL created")
        print(f"   Opening browser to: {auth_url[:50]}...")
        
        # Open browser
        webbrowser.open(auth_url)
        
        print("\n💡 Instructions:")
        print("1. Your browser should open automatically")
        print("2. Sign in with your Microsoft account")
        print("3. After authentication, you'll be redirected to localhost")
        print("4. Copy the FULL URL from your browser address bar")
        print("5. Paste it here")
        
        # Get the redirect URL from user
        redirect_response = input("\nPaste the full redirect URL here: ").strip()
        
        if not redirect_response:
            print("❌ No URL provided")
            return False
        
        # Parse the authorization code from the URL
        parsed_url = urlparse(redirect_response)
        query_params = parse_qs(parsed_url.query)
        
        if 'code' not in query_params:
            print(f"❌ No authorization code found in URL: {redirect_response}")
            if 'error' in query_params:
                print(f"   Error: {query_params['error'][0]}")
                if 'error_description' in query_params:
                    print(f"   Description: {query_params['error_description'][0]}")
            return False
        
        auth_code = query_params['code'][0]
        print(f"✅ Authorization code extracted: {auth_code[:20]}...")
        
        # Exchange code for token
        result = app.acquire_token_by_authorization_code(
            auth_code,
            scopes=scope,
            redirect_uri=redirect_uri
        )
        
        if "access_token" in result:
            print("✅ Authentication successful!")
            print(f"   Access token length: {len(result['access_token'])}")
            print(f"   Token type: {result.get('token_type', 'Unknown')}")
            print(f"   Expires in: {result.get('expires_in', 'Unknown')} seconds")
            return True
        else:
            print(f"❌ Token exchange failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Browser authentication error: {e}")
        return False

if __name__ == "__main__":
    success = test_browser_oauth2()
    if success:
        print("\n🎉 OAuth2 browser test completed successfully!")
        print("   Your OAuth2 setup is working correctly")
    else:
        print("\n❌ OAuth2 browser test failed!")
        print("   Please check your Azure app configuration")
