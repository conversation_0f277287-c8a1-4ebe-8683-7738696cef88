#!/usr/bin/env python3
"""
Simple OAuth2 Test - Direct MSAL test
"""

import msal
import json
import os

def test_simple_oauth2():
    """Simple OAuth2 test with MSAL"""
    print("🔐 Simple OAuth2 Test")
    print("=" * 30)
    
    # Load config
    config_file = "oauth2_config.json"
    if not os.path.exists(config_file):
        print(f"❌ Config file not found: {config_file}")
        return False
    
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    client_id = config.get('client_id')
    if not client_id:
        print("❌ No client_id in config")
        return False
    
    print(f"✅ Client ID: {client_id[:12]}...")
    
    # Create MSAL app
    authority = "https://login.microsoftonline.com/consumers"
    scope = ["https://graph.microsoft.com/Mail.Send"]
    
    try:
        app = msal.PublicClientApplication(
            client_id,
            authority=authority,
        )
        print("✅ MSAL app created")
    except Exception as e:
        print(f"❌ Failed to create MSAL app: {e}")
        return False
    
    # Try to initiate device flow
    try:
        flow = app.initiate_device_flow(scopes=scope)
        if "user_code" not in flow:
            print(f"❌ Device flow failed: {flow}")
            return False
        
        print("✅ Device flow created successfully!")
        print(f"   Verification URI: {flow['verification_uri']}")
        print(f"   User Code: {flow['user_code']}")
        print(f"   Message: {flow.get('message', 'No message')}")
        
        # Ask user if they want to continue
        print("\n💡 To complete authentication:")
        print(f"1. Go to: {flow['verification_uri']}")
        print(f"2. Enter code: {flow['user_code']}")
        print("3. Sign in with your Microsoft account")
        
        choice = input("\nDo you want to complete the authentication? (y/N): ").lower()
        if choice == 'y':
            print("Waiting for authentication...")
            result = app.acquire_token_by_device_flow(flow)
            
            if "access_token" in result:
                print("✅ Authentication successful!")
                print(f"   Access token length: {len(result['access_token'])}")
                return True
            else:
                print(f"❌ Authentication failed: {result}")
                return False
        else:
            print("Authentication skipped")
            return True
            
    except Exception as e:
        print(f"❌ Device flow error: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_oauth2()
    if success:
        print("\n🎉 OAuth2 test completed successfully!")
    else:
        print("\n❌ OAuth2 test failed!")
