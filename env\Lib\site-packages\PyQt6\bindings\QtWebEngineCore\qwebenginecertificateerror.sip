// qwebenginecertificateerror.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineCertificateError /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebenginecertificateerror.h>
%End

public:
    enum Type
    {
        SslPinnedKeyNotInCertificateChain,
        CertificateCommonNameInvalid,
        CertificateDateInvalid,
        CertificateAuthorityInvalid,
        CertificateContainsErrors,
        CertificateNoRevocationMechanism,
        CertificateUnableToCheckRevocation,
        CertificateRevoked,
        CertificateInvalid,
        CertificateWeakSignatureAlgorithm,
        CertificateNonUniqueName,
        CertificateWeakKey,
        CertificateNameConstraintViolation,
        CertificateValidityTooLong,
        CertificateTransparencyRequired,
        CertificateSymantecLegacy,
        CertificateKnownInterceptionBlocked,
        SslObsoleteVersion,
    };

    QUrl url() const;
    bool isOverridable() const;
    QWebEngineCertificateError(const QWebEngineCertificateError &other);
    void defer();
    void rejectCertificate();
    QList<QSslCertificate> certificateChain() const;
    QWebEngineCertificateError::Type type() const;
    QString description() const;
    void acceptCertificate();
%If (QtWebEngine_6_8_0 -)
    bool isMainFrame() const;
%End
};
