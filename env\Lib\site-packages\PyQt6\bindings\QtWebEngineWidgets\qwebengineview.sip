// qwebengineview.sip generated by MetaSIP
//
// This file is part of the QtWebEngineWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineView : public QWidget
{
%TypeHeaderCode
#include <qwebengineview.h>
%End

%TypeCode
// For QWebEngineView.findText().
#include <QWebEngineFindTextResult>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QWebEngineView, &sipType_QWebEngineView, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
%If (QtWebEngine_6_4_0 -)
    QWebEngineView(QWebEnginePage *page, QWidget *parent /TransferThis/ = 0);
%End
%If (QtWebEngine_6_4_0 -)
    QWebEngineView(QWebEngineProfile *profile, QWidget *parent /TransferThis/ = 0);
%End
    explicit QWebEngineView(QWidget *parent /TransferThis/ = 0);
    virtual ~QWebEngineView();
    QWebEnginePage *page() const;
    void setPage(QWebEnginePage *page);
    void load(const QUrl &url);
    void load(const QWebEngineHttpRequest &request);
    void setHtml(const QString &html, const QUrl &baseUrl = QUrl());
    void setContent(const QByteArray &data, const QString &mimeType = QString(), const QUrl &baseUrl = QUrl());
    QWebEngineHistory *history() const;
    QString title() const;
    void setUrl(const QUrl &url);
    QUrl url() const;
    QUrl iconUrl() const;
    bool hasSelection() const;
    QString selectedText() const;
    QAction *pageAction(QWebEnginePage::WebAction action) const;
    void triggerPageAction(QWebEnginePage::WebAction action, bool checked = false);
    qreal zoomFactor() const;
    void setZoomFactor(qreal factor);
    virtual QSize sizeHint() const;
    QWebEngineSettings *settings() const;
    QIcon icon() const;

public slots:
    void stop();
    void back();
    void forward();
    void reload();

signals:
    void loadStarted();
    void loadProgress(int progress);
    void loadFinished(bool);
    void titleChanged(const QString &title);
    void selectionChanged();
    void urlChanged(const QUrl &);
    void iconUrlChanged(const QUrl &);
    void iconChanged(const QIcon &);
    void renderProcessTerminated(QWebEnginePage::RenderProcessTerminationStatus terminationStatus, int exitCode);

protected:
    virtual QWebEngineView *createWindow(QWebEnginePage::WebWindowType type);
    virtual void contextMenuEvent(QContextMenuEvent *);
    virtual bool event(QEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void hideEvent(QHideEvent *);
    virtual void dragEnterEvent(QDragEnterEvent *e);
    virtual void dragLeaveEvent(QDragLeaveEvent *e);
    virtual void dragMoveEvent(QDragMoveEvent *e);
    virtual void dropEvent(QDropEvent *e);
    virtual void closeEvent(QCloseEvent *);

public:
    static QWebEngineView *forPage(const QWebEnginePage *page);
    void findText(const QString &subString, QWebEnginePage::FindFlags options = {}, SIP_PYCALLABLE resultCallback /AllowNone,TypeHint="Callable[[bool], None]"/ = 0);
%MethodCode
        // Make sure any callable doesn't get garbage collected until it is invoked.
        Py_XINCREF(a2);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->findText(*a0, *a1, [a2](const QWebEngineFindTextResult &arg) {
            if (a2)
            {
                SIP_BLOCK_THREADS
                
                PyObject *res;
        
                // We have to find the generated type structure because it isn't
                // automatically generated as it isn't used anywhere else in the
                // module.
                res = sipCallMethod(NULL, a2, "N", new QWebEngineFindTextResult(arg), sipFindType("QWebEngineFindTextResult"), NULL);
        
                Py_DECREF(a2);
        
                if (!res)
                    pyqt6_qtwebenginewidgets_err_print();
                else
                    Py_DECREF(res);
        
                SIP_UNBLOCK_THREADS
            }
        });
        
        Py_END_ALLOW_THREADS
%End

    QMenu *createStandardContextMenu() /Factory/;
    QWebEngineContextMenuRequest *lastContextMenuRequest() const;
    void printToPdf(const QString &filePath, const QPageLayout &pageLayout = QPageLayout(QPageSize(QPageSize::A4), QPageLayout::Portrait, QMarginsF()), const QPageRanges &ranges = {});
    void printToPdf(SIP_PYCALLABLE resultCallback /TypeHint="Callable[[QByteArray], None]"/, const QPageLayout &pageLayout = QPageLayout(QPageSize(QPageSize::A4), QPageLayout::Portrait, QMarginsF()), const QPageRanges &ranges = {});
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->printToPdf([a0](const QByteArray &arg) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a0, "N", new QByteArray(arg), sipType_QByteArray, NULL);
        
            Py_DECREF(a0);
        
            if (!res)
                pyqt6_qtwebenginewidgets_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        }, *a1, *a2);
        
        Py_END_ALLOW_THREADS
%End

    void print(QPrinter *printer);

signals:
    void pdfPrintingFinished(const QString &filePath, bool success);
    void printRequested();
    void printFinished(bool success);
%If (QtWebEngine_6_8_0 -)
    void printRequestedByFrame(QWebEngineFrame frame);
%End
};

%ModuleHeaderCode
// Imports from QtCore.
typedef void (*pyqt6_qtwebenginewidgets_err_print_t)();
extern pyqt6_qtwebenginewidgets_err_print_t pyqt6_qtwebenginewidgets_err_print;
%End

%ModuleCode
// Imports from QtCore.
pyqt6_qtwebenginewidgets_err_print_t pyqt6_qtwebenginewidgets_err_print;

// Needed by the %PreInitialisationCode.
#include <QCoreApplication>
%End

%PreInitialisationCode
// QtWebEngineWidgets uses Q_COREAPP_STARTUP_FUNCTION so, in order to make sure
// things work (particularly on Windows) when we dynamically load the code, we
// check things have been done in the right order.
if (QCoreApplication::instance() && !QCoreApplication::testAttribute(Qt::AA_ShareOpenGLContexts))
{
    PyErr_SetString(PyExc_ImportError,
            "QtWebEngineWidgets must be imported or Qt.AA_ShareOpenGLContexts must be set before a QCoreApplication instance is created");
    return SIP_NULLPTR;
}
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtwebenginewidgets_err_print = (pyqt6_qtwebenginewidgets_err_print_t)sipImportSymbol("pyqt6_err_print");
Q_ASSERT(pyqt6_qtwebenginewidgets_err_print);
%End
