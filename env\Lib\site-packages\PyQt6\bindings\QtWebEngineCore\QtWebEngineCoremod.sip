// QtWebEngineCoremod.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtWebEngineCore, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip
%Import QtGui/QtGuimod.sip
%Import QtNetwork/QtNetworkmod.sip
%Import QtWebChannel/QtWebChannelmod.sip

%Timeline {QtWebEngine_6_0_0 QtWebEngine_6_1_0 QtWebEngine_6_2_0 QtWebEngine_6_3_0 QtWebEngine_6_4_0 QtWebEngine_6_5_0 QtWebEngine_6_6_0 QtWebEngine_6_7_0 QtWebEngine_6_8_0 QtWebEngine_6_9_0}

%Copying
Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6-WebEngine.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype PyQt6.sip.simplewrapper

int PYQT_WEBENGINE_VERSION;
const char *PYQT_WEBENGINE_VERSION_STR;

%ModuleCode
static int PYQT_WEBENGINE_VERSION = 0x060900;
static const char *PYQT_WEBENGINE_VERSION_STR = "6.9.0";
%End

%Include qtwebenginecoreglobal.sip
%Include qwebenginecertificateerror.sip
%Include qwebengineclientcertificateselection.sip
%Include qwebengineclientcertificatestore.sip
%Include qwebengineclienthints.sip
%Include qwebenginecontextmenurequest.sip
%Include qwebenginecookiestore.sip
%Include qwebenginedesktopmediarequest.sip
%Include qwebenginedownloadrequest.sip
%Include qwebenginefilesystemaccessrequest.sip
%Include qwebenginefindtextresult.sip
%Include qwebengineframe.sip
%Include qwebenginefullscreenrequest.sip
%Include qwebengineglobalsettings.sip
%Include qwebenginehistory.sip
%Include qwebenginehttprequest.sip
%Include qwebengineloadinginfo.sip
%Include qwebenginenavigationrequest.sip
%Include qwebenginenewwindowrequest.sip
%Include qwebenginenotification.sip
%Include qwebenginepage.sip
%Include qwebenginepermission.sip
%Include qwebengineprofile.sip
%Include qwebengineprofilebuilder.sip
%Include qwebenginequotarequest.sip
%Include qwebengineregisterprotocolhandlerrequest.sip
%Include qwebenginescript.sip
%Include qwebenginescriptcollection.sip
%Include qwebenginesettings.sip
%Include qwebengineurlrequestinfo.sip
%Include qwebengineurlrequestinterceptor.sip
%Include qwebengineurlrequestjob.sip
%Include qwebengineurlscheme.sip
%Include qwebengineurlschemehandler.sip
%Include qwebenginewebauthuxrequest.sip
