// qwebengineurlrequestinfo.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineUrlRequestInfo /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebengineurlrequestinfo.h>
%End

public:
    enum ResourceType
    {
        ResourceTypeMainFrame,
        ResourceTypeSubFrame,
        ResourceTypeStylesheet,
        ResourceTypeScript,
        ResourceTypeImage,
        ResourceTypeFontResource,
        ResourceTypeSubResource,
        ResourceTypeObject,
        ResourceTypeMedia,
        ResourceTypeWorker,
        ResourceTypeSharedWorker,
        ResourceTypePrefetch,
        ResourceTypeFavicon,
        ResourceTypeXhr,
        ResourceTypePing,
        ResourceTypeServiceWorker,
        ResourceTypeUnknown,
        ResourceTypeCspReport,
        ResourceTypePluginResource,
        ResourceTypeNavigationPreloadMainFrame,
        ResourceTypeNavigationPreloadSubFrame,
%If (QtWebEngine_6_4_0 -)
        ResourceTypeWebSocket,
%End
%If (QtWebEngine_6_8_0 -)
        ResourceTypeJson,
%End
    };

    enum NavigationType
    {
        NavigationTypeLink,
        NavigationTypeTyped,
        NavigationTypeFormSubmitted,
        NavigationTypeBackForward,
        NavigationTypeReload,
        NavigationTypeRedirect,
        NavigationTypeOther,
    };

    QWebEngineUrlRequestInfo::ResourceType resourceType() const;
    QWebEngineUrlRequestInfo::NavigationType navigationType() const;
    QUrl requestUrl() const;
    QUrl firstPartyUrl() const;
    QByteArray requestMethod() const;
    void block(bool shouldBlock);
    void redirect(const QUrl &url);
    void setHttpHeader(const QByteArray &name, const QByteArray &value);
    QUrl initiator() const;
%If (QtWebEngine_6_5_0 -)
    QHash<QByteArray, QByteArray> httpHeaders() const;
%End
%If (QtWebEngine_6_7_0 -)
    QIODevice *requestBody() const;
%End
%If (QtWebEngine_6_9_0 -)
    bool isDownload() const;
%End

private:
    QWebEngineUrlRequestInfo();
    ~QWebEngineUrlRequestInfo();
};
