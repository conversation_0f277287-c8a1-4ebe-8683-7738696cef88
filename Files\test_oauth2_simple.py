#!/usr/bin/env python3
"""
Simple test for OAuth2 functionality without PyQt6 dependencies
"""

import sys
import os
import json

# Test the OAuth2 class without importing the full smtp.py
def test_oauth2_class():
    """Test OAuth2 class functionality"""
    print("🧪 Testing OAuth2 Class...")
    
    # Mock the OAuth2 class functionality
    class MockOAuth2SMTPAuth:
        def __init__(self):
            self.client_id = None
            self.client_secret = None
            self.tenant_id = None
            self.authority = None
            self.scope = ["https://graph.microsoft.com/Mail.Send"]
            self.account_type = "personal"
        
        def is_microsoft_domain(self, email):
            """Check if email domain requires OAuth2 authentication"""
            domain = email.split('@')[1].lower() if '@' in email else ''
            microsoft_domains = [
                'outlook.com', 'hotmail.com', 'live.com', 'msn.com',
                'outlook.office365.com', 'office365.com'
            ]
            return any(domain.endswith(ms_domain) for ms_domain in microsoft_domains)
        
        def configure_oauth2(self, client_id=None, client_secret=None, tenant_id=None, account_type=None):
            """Configure OAuth2 credentials"""
            self.client_id = client_id
            self.client_secret = client_secret
            self.tenant_id = tenant_id
            if account_type:
                self.account_type = account_type
            
            # Try to load from config file
            config_file = "oauth2_config.json"
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r') as f:
                        config = json.load(f)
                        if not self.client_id:
                            self.client_id = config.get('client_id')
                        if not self.client_secret:
                            self.client_secret = config.get('client_secret')
                        if not self.tenant_id:
                            self.tenant_id = config.get('tenant_id')
                        if not account_type:
                            self.account_type = config.get('account_type', 'personal')
                except Exception as e:
                    print(f"Warning: Failed to load config: {e}")
            
            # Set authority and scope based on account type
            if self.account_type == "business" and self.tenant_id:
                self.authority = f"https://login.microsoftonline.com/{self.tenant_id}"
                self.scope = ["https://outlook.office365.com/.default"]
            else:
                # Personal account (default)
                self.authority = "https://login.microsoftonline.com/common"
                self.scope = ["https://graph.microsoft.com/Mail.Send"]
                self.account_type = "personal"
        
        def create_xoauth2_string(self, email, access_token):
            """Create XOAUTH2 authentication string for SMTP"""
            import base64
            auth_string = f"user={email}\x01auth=Bearer {access_token}\x01\x01"
            return base64.b64encode(auth_string.encode()).decode()
    
    # Test the class
    oauth2_handler = MockOAuth2SMTPAuth()
    
    # Test domain detection
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    print("\n📧 Testing domain detection:")
    for email in test_emails:
        is_ms = oauth2_handler.is_microsoft_domain(email)
        print(f"   {email}: {'✅ Microsoft domain' if is_ms else '❌ Non-Microsoft domain'}")
    
    # Test configuration
    print("\n⚙️ Testing configuration:")
    oauth2_handler.configure_oauth2()
    
    print(f"   Account Type: {oauth2_handler.account_type}")
    print(f"   Authority: {oauth2_handler.authority}")
    print(f"   Scope: {oauth2_handler.scope}")
    
    if oauth2_handler.client_id:
        print(f"   ✅ Client ID configured: {oauth2_handler.client_id[:8]}...")
    else:
        print("   ⚠️ Client ID not configured")
    
    # Test XOAUTH2 string creation
    print("\n🔑 Testing XOAUTH2 string creation:")
    test_token = "fake_access_token_for_testing"
    test_email = "<EMAIL>"
    
    try:
        xoauth2_string = oauth2_handler.create_xoauth2_string(test_email, test_token)
        print(f"   ✅ XOAUTH2 string created (length: {len(xoauth2_string)} characters)")
        
        # Decode and verify format
        import base64
        decoded = base64.b64decode(xoauth2_string).decode()
        expected_start = f"user={test_email}\x01auth=Bearer {test_token}"
        if decoded.startswith(expected_start):
            print("   ✅ XOAUTH2 string format is correct")
        else:
            print("   ❌ XOAUTH2 string format is incorrect")
    except Exception as e:
        print(f"   ❌ XOAUTH2 string creation failed: {e}")
    
    return True

def test_config_file():
    """Test configuration file"""
    print("\n📄 Testing configuration file...")
    
    config_file = "oauth2_config.json"
    if os.path.exists(config_file):
        print(f"   ✅ Configuration file exists: {config_file}")
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            account_type = config.get('account_type', 'personal')
            print(f"   Account Type: {account_type}")
            
            if account_type == "personal":
                required_keys = ['client_id']
                optional_keys = ['client_secret', 'tenant_id']
            else:
                required_keys = ['client_id', 'client_secret', 'tenant_id']
                optional_keys = []
            
            for key in required_keys:
                if key in config and config[key]:
                    print(f"   ✅ {key}: configured")
                else:
                    print(f"   ❌ {key}: missing or empty")
            
            for key in optional_keys:
                if key in config and config[key]:
                    print(f"   ℹ️ {key}: configured (not needed for personal accounts)")
                else:
                    print(f"   ℹ️ {key}: not configured (not needed for personal accounts)")
            
            return True
        except Exception as e:
            print(f"   ❌ Error reading config file: {e}")
            return False
    else:
        print(f"   ⚠️ Configuration file not found: {config_file}")
        print("   💡 Run 'python setup_personal_oauth2.py' to create configuration")
        return False

def main():
    """Main test function"""
    print("🚀 OAuth2 Simple Test Suite")
    print("=" * 60)
    
    tests = [
        ("OAuth2 Class", test_oauth2_class),
        ("Configuration File", test_config_file)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 OAuth2 logic is working correctly!")
    else:
        print("⚠️ Some tests failed. Check the configuration.")
    
    print("\n💡 Next Steps:")
    print("1. Run 'python setup_personal_oauth2.py' for guided setup")
    print("2. Install MSAL: pip install msal")
    print("3. Test with your actual email sending application")

if __name__ == "__main__":
    main()
