#!/usr/bin/env python3
"""
Simple test for OAuth2 functionality without PyQt6 dependencies
"""

import sys
import os
import json

# Test the OAuth2 class without importing the full smtp.py
def test_oauth2_class():
    """Test OAuth2 class functionality"""
    print("🧪 Testing OAuth2 Class...")
    
    # Mock the OAuth2 class functionality
    class MockOAuth2SMTPAuth:
        def __init__(self):
            self.client_id = None
            self.client_secret = None
            self.tenant_id = None
            self.authority = None
            self.scope = ["https://graph.microsoft.com/Mail.Send"]
            self.account_type = "personal"
        
        def is_microsoft_domain(self, email):
            """Check if email domain requires OAuth2 authentication"""
            domain = email.split('@')[1].lower() if '@' in email else ''
            microsoft_domains = [
                'outlook.com', 'hotmail.com', 'live.com', 'msn.com',
                'outlook.office365.com', 'office365.com'
            ]
            return any(domain.endswith(ms_domain) for ms_domain in microsoft_domains)
        
        def configure_oauth2(self, client_id=None, client_secret=None, tenant_id=None, account_type=None):
            """Configure OAuth2 credentials"""
            self.client_id = client_id
            self.client_secret = client_secret
            self.tenant_id = tenant_id
            if account_type:
                self.account_type = account_type
            
            # Try to load from config file
            config_file = "oauth2_config.json"
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r') as f:
                        config = json.load(f)
                        if not self.client_id:
                            self.client_id = config.get('client_id')
                        if not self.client_secret:
                            self.client_secret = config.get('client_secret')
                        if not self.tenant_id:
                            self.tenant_id = config.get('tenant_id')
                        if not account_type:
                            self.account_type = config.get('account_type', 'personal')
                except Exception as e:
                    print(f"Warning: Failed to load config: {e}")
            
            # Set authority and scope based on account type
            if self.account_type == "business" and self.tenant_id:
                self.authority = f"https://login.microsoftonline.com/{self.tenant_id}"
                self.scope = ["https://outlook.office365.com/.default"]
            else:
                # Personal account (default)
                self.authority = "https://login.microsoftonline.com/common"
                self.scope = ["https://graph.microsoft.com/Mail.Send"]
                self.account_type = "personal"
        
        def create_xoauth2_string(self, email, access_token):
            """Create XOAUTH2 authentication string for SMTP"""
            import base64
            auth_string = f"user={email}\x01auth=Bearer {access_token}\x01\x01"
            return base64.b64encode(auth_string.encode()).decode()
    
    # Test the class
    oauth2_handler = MockOAuth2SMTPAuth()
    
    # Test domain detection
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    print("\n📧 Testing domain detection:")
    for email in test_emails:
        is_ms = oauth2_handler.is_microsoft_domain(email)
        print(f"   {email}: {'✅ Microsoft domain' if is_ms else '❌ Non-Microsoft domain'}")
    
    # Test configuration
    print("\n⚙️ Testing configuration:")
    oauth2_handler.configure_oauth2()
    
    print(f"   Account Type: {oauth2_handler.account_type}")
    print(f"   Authority: {oauth2_handler.authority}")
    print(f"   Scope: {oauth2_handler.scope}")
    
    if oauth2_handler.client_id:
        print(f"   ✅ Client ID configured: {oauth2_handler.client_id[:8]}...")
    else:
        print("   ⚠️ Client ID not configured")
    
    # Test XOAUTH2 string creation
    print("\n🔑 Testing XOAUTH2 string creation:")
    test_token = "fake_access_token_for_testing"
    test_email = "<EMAIL>"
    
    try:
        xoauth2_string = oauth2_handler.create_xoauth2_string(test_email, test_token)
        print(f"   ✅ XOAUTH2 string created (length: {len(xoauth2_string)} characters)")
        
        # Decode and verify format
        import base64
        decoded = base64.b64decode(xoauth2_string).decode()
        expected_start = f"user={test_email}\x01auth=Bearer {test_token}"
        if decoded.startswith(expected_start):
            print("   ✅ XOAUTH2 string format is correct")
        else:
            print("   ❌ XOAUTH2 string format is incorrect")
    except Exception as e:
        print(f"   ❌ XOAUTH2 string creation failed: {e}")
    
    return True

def test_config_file():
    """Test configuration file and return config data"""
    print("\n📄 Testing configuration file...")

    config_file = "oauth2_config.json"
    if os.path.exists(config_file):
        print(f"   ✅ Configuration file exists: {config_file}")
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)

            account_type = config.get('account_type', 'personal')
            print(f"   Account Type: {account_type}")

            if account_type == "personal":
                required_keys = ['client_id']
                optional_keys = ['client_secret', 'tenant_id']
            else:
                required_keys = ['client_id', 'client_secret', 'tenant_id']
                optional_keys = []

            all_required_present = True
            for key in required_keys:
                if key in config and config[key] and config[key] != "your-application-client-id-here":
                    print(f"   ✅ {key}: configured")
                else:
                    print(f"   ❌ {key}: missing or empty")
                    all_required_present = False

            for key in optional_keys:
                if key in config and config[key] and config[key] not in ["only-needed-for-business-accounts", "your-client-secret-here", "your-tenant-id-here"]:
                    print(f"   ℹ️ {key}: configured (not needed for personal accounts)")
                else:
                    print(f"   ℹ️ {key}: not configured (not needed for personal accounts)")

            return config if all_required_present else None
        except Exception as e:
            print(f"   ❌ Error reading config file: {e}")
            return None
    else:
        print(f"   ⚠️ Configuration file not found: {config_file}")
        print("   💡 Run 'python setup_personal_oauth2.py' to create configuration")
        return None


def test_msal_availability():
    """Test if MSAL is available"""
    print("\n📚 Testing MSAL availability...")

    try:
        import msal
        print(f"   ✅ MSAL library is available (version: {msal.__version__})")
        return True
    except ImportError:
        print("   ❌ MSAL library not available")
        print("   💡 Install with: pip install msal")
        return False


def test_oauth2_authentication(config):
    """Test actual OAuth2 authentication if config is available"""
    print("\n🔐 Testing OAuth2 Authentication...")

    if not config:
        print("   ⚠️ No valid configuration available, skipping authentication test")
        return False

    # Check if MSAL is available
    try:
        import msal
    except ImportError:
        print("   ❌ MSAL not available, cannot test authentication")
        return False

    account_type = config.get('account_type', 'personal')
    client_id = config.get('client_id')

    print(f"   Account Type: {account_type}")
    print(f"   Client ID: {client_id[:8]}..." if client_id else "   Client ID: Not configured")

    try:
        if account_type == "personal":
            # Test personal account OAuth2 setup
            authority = "https://login.microsoftonline.com/common"
            scope = ["https://graph.microsoft.com/Mail.Send"]

            print(f"   Authority: {authority}")
            print(f"   Scope: {scope}")

            # Create MSAL app for personal accounts
            app = msal.PublicClientApplication(
                client_id,
                authority=authority,
            )

            print("   ✅ MSAL PublicClientApplication created successfully")

            # Check for cached tokens
            accounts = app.get_accounts()
            if accounts:
                print(f"   ✅ Found {len(accounts)} cached account(s)")

                # Try silent token acquisition
                result = app.acquire_token_silent(scope, account=accounts[0])
                if result and "access_token" in result:
                    print("   ✅ Successfully acquired token silently from cache!")
                    print(f"   Token length: {len(result['access_token'])} characters")

                    # Test XOAUTH2 string creation
                    test_email = "<EMAIL>"
                    import base64
                    auth_string = f"user={test_email}\x01auth=Bearer {result['access_token']}\x01\x01"
                    xoauth2_string = base64.b64encode(auth_string.encode()).decode()
                    print(f"   ✅ XOAUTH2 string created (length: {len(xoauth2_string)} characters)")

                    return True
                else:
                    print("   ⚠️ Cached token expired or invalid")
            else:
                print("   ℹ️ No cached accounts found")

            # Offer interactive authentication
            print("\n   💡 To test full authentication:")
            response = input("   Do you want to test interactive authentication? (y/N): ").strip().lower()

            if response == 'y':
                print("   🔄 Starting device code flow...")

                flow = app.initiate_device_flow(scopes=scope)
                if "user_code" not in flow:
                    print("   ❌ Failed to create device flow")
                    return False

                print(f"\n   🔐 OAuth2 Authentication Required:")
                print(f"   1. Go to: {flow['verification_uri']}")
                print(f"   2. Enter code: {flow['user_code']}")
                print("   3. Sign in with your Microsoft account")
                print("   4. Return here and press Enter when done...")
                input("   Press Enter after completing authentication...")

                result = app.acquire_token_by_device_flow(flow)

                if "access_token" in result:
                    print("   ✅ Interactive authentication successful!")
                    print(f"   Token length: {len(result['access_token'])} characters")

                    # Test XOAUTH2 string creation
                    test_email = "<EMAIL>"
                    import base64
                    auth_string = f"user={test_email}\x01auth=Bearer {result['access_token']}\x01\x01"
                    xoauth2_string = base64.b64encode(auth_string.encode()).decode()
                    print(f"   ✅ XOAUTH2 string created (length: {len(xoauth2_string)} characters)")

                    return True
                else:
                    error_msg = result.get("error_description", result.get("error", "Unknown error"))
                    print(f"   ❌ Authentication failed: {error_msg}")
                    return False
            else:
                print("   ℹ️ Interactive authentication skipped")
                print("   ✅ OAuth2 setup appears correct (client app created successfully)")
                return True

        else:
            # Business account
            client_secret = config.get('client_secret')
            tenant_id = config.get('tenant_id')

            if not all([client_secret, tenant_id]):
                print("   ❌ Business account requires client_secret and tenant_id")
                return False

            authority = f"https://login.microsoftonline.com/{tenant_id}"
            scope = ["https://outlook.office365.com/.default"]

            print(f"   Authority: {authority}")
            print(f"   Scope: {scope}")

            # Create MSAL app for business accounts
            app = msal.ConfidentialClientApplication(
                client_id,
                authority=authority,
                client_credential=client_secret,
            )

            print("   ✅ MSAL ConfidentialClientApplication created successfully")

            # Try to acquire token
            result = app.acquire_token_for_client(scopes=scope)

            if "access_token" in result:
                print("   ✅ Business authentication successful!")
                print(f"   Token length: {len(result['access_token'])} characters")

                # Test XOAUTH2 string creation
                test_email = "<EMAIL>"
                import base64
                auth_string = f"user={test_email}\x01auth=Bearer {result['access_token']}\x01\x01"
                xoauth2_string = base64.b64encode(auth_string.encode()).decode()
                print(f"   ✅ XOAUTH2 string created (length: {len(xoauth2_string)} characters)")

                return True
            else:
                error_msg = result.get("error_description", result.get("error", "Unknown error"))
                print(f"   ❌ Business authentication failed: {error_msg}")
                return False

    except Exception as e:
        print(f"   ❌ OAuth2 authentication test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 OAuth2 Comprehensive Test Suite")
    print("   Tests both Personal and Business OAuth2 configurations")
    print("=" * 60)

    # First run basic tests
    basic_tests = [
        ("OAuth2 Class Logic", test_oauth2_class),
        ("MSAL Library", test_msal_availability)
    ]

    results = []
    for test_name, test_func in basic_tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append((test_name, False))

    # Test configuration and authentication
    print(f"\n🧪 Running Configuration test...")
    try:
        config = test_config_file()
        config_result = config is not None
        results.append(("Configuration File", config_result))

        if config_result:
            print(f"\n🧪 Running OAuth2 Authentication test...")
            auth_result = test_oauth2_authentication(config)
            results.append(("OAuth2 Authentication", auth_result))
        else:
            print("\n⚠️ Skipping OAuth2 Authentication test (no valid configuration)")
            results.append(("OAuth2 Authentication", False))

    except Exception as e:
        print(f"   ❌ Configuration test failed with exception: {e}")
        results.append(("Configuration File", False))
        results.append(("OAuth2 Authentication", False))

    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")

    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1

    print(f"\nOverall: {passed}/{len(results)} tests passed")

    # Detailed feedback based on results
    if passed == len(results):
        print("🎉 All tests passed! OAuth2 authentication is fully configured and working!")
        print("\n✅ Your setup is ready for:")
        print("   - Automatic OAuth2 authentication for Microsoft domains")
        print("   - Both personal (@hotmail.com, @live.com, @outlook.com) and business accounts")
        print("   - Fallback to app passwords if needed")
    elif passed >= len(results) - 1:
        print("🎯 Almost there! OAuth2 is mostly configured.")
        if not results[-1][1]:  # If authentication failed
            print("   💡 Authentication test failed - this might be normal if:")
            print("   - You haven't completed interactive authentication yet")
            print("   - Your credentials need verification")
            print("   - You're using a business account without proper permissions")
    else:
        print("⚠️ Several tests failed. Check the configuration and setup.")

    print("\n💡 Next Steps:")
    if not any(name == "MSAL Library" and result for name, result in results):
        print("1. 🔧 Install MSAL: pip install msal")
    if not any(name == "Configuration File" and result for name, result in results):
        print("2. ⚙️ Run setup: python setup_personal_oauth2.py")
    if not any(name == "OAuth2 Authentication" and result for name, result in results):
        print("3. 🔐 Complete interactive authentication when prompted")

    print("4. 🚀 Test with your actual email sending application")
    print("\n📋 Configuration Summary:")

    # Show what type of account is configured
    try:
        if os.path.exists("oauth2_config.json"):
            with open("oauth2_config.json", 'r') as f:
                config = json.load(f)
            account_type = config.get('account_type', 'personal')
            if account_type == "personal":
                print("   Account Type: Personal Microsoft Account")
                print("   Domains: @hotmail.com, @live.com, @outlook.com")
                print("   Authentication: Interactive device code flow")
            else:
                print("   Account Type: Business Microsoft Account")
                print("   Domains: Custom business domains")
                print("   Authentication: Client credentials flow")
        else:
            print("   No configuration file found")
    except:
        pass

if __name__ == "__main__":
    main()
