#!/usr/bin/env python3
"""
Real OAuth2 Test - Test with actual Microsoft account
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the OAuth2 handler from smtp.py
from smtp import OAuth2SMTPAuth

def test_real_oauth2():
    """Test OAuth2 with real Microsoft account"""
    print("🔐 Real OAuth2 Authentication Test")
    print("=" * 50)
    
    # Create OAuth2 handler
    oauth2_handler = OAuth2SMTPAuth()
    
    # Configure OAuth2
    try:
        oauth2_handler.configure_oauth2()
        print(f"✅ OAuth2 configured successfully")
        print(f"   Account Type: {oauth2_handler.account_type}")
        print(f"   Authority: {oauth2_handler.authority}")
        print(f"   Scope: {oauth2_handler.scope}")
        print(f"   Client ID: {oauth2_handler.client_id[:12]}...")
    except Exception as e:
        print(f"❌ OAuth2 configuration failed: {e}")
        return False
    
    # Test getting access token
    print("\n🔑 Testing access token acquisition...")
    try:
        access_token = oauth2_handler.get_access_token()
        if access_token:
            print(f"✅ Access token acquired successfully!")
            print(f"   Token length: {len(access_token)} characters")
            print(f"   Token preview: {access_token[:20]}...")
            
            # Test creating XOAUTH2 string
            test_email = "<EMAIL>"
            xoauth2_string = oauth2_handler.create_xoauth2_string(test_email, access_token)
            print(f"✅ XOAUTH2 string created successfully!")
            print(f"   String length: {len(xoauth2_string)} characters")
            
            return True
        else:
            print("❌ No access token received")
            return False
            
    except Exception as e:
        print(f"❌ Access token acquisition failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing OAuth2 with Real Microsoft Account")
    print("   This will open a browser for authentication")
    print("   Make sure you have a Microsoft account ready")
    print()
    
    input("Press Enter to continue...")
    
    success = test_real_oauth2()
    
    if success:
        print("\n🎉 OAuth2 authentication test PASSED!")
        print("   Your OAuth2 setup is working correctly")
        print("   You can now use OAuth2 authentication in your SMTP application")
    else:
        print("\n❌ OAuth2 authentication test FAILED!")
        print("   Please check your configuration and try again")
