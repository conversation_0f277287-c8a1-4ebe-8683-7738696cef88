// qwebengineloadinginfo.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineLoadingInfo
{
%TypeHeaderCode
#include <qwebengineloadinginfo.h>
%End

public:
    enum LoadStatus
    {
        LoadStartedStatus,
        LoadStoppedStatus,
        LoadSucceededStatus,
        LoadFailedStatus,
    };

    enum ErrorDomain
    {
        NoErrorDomain,
        InternalErrorDomain,
        ConnectionErrorDomain,
        CertificateErrorDomain,
        HttpErrorDomain,
        FtpErrorDomain,
        DnsErrorDomain,
%If (QtWebEngine_6_4_0 -)
        HttpStatusCodeDomain,
%End
    };

private:
%If (QtWebEngine_6_9_0 -)
    QWebEngineLoadingInfo(const QUrl &url, QWebEngineLoadingInfo::LoadStatus status, bool isErrorPage = false, const QString &errorString = QString(), int errorCode = 0, QWebEngineLoadingInfo::ErrorDomain errorDomain = QWebEngineLoadingInfo::NoErrorDomain, const QMultiMap<QByteArray, QByteArray> &responseHeaders = {}, bool isDownload = false);
%End
%If (QtWebEngine_6_6_0 - QtWebEngine_6_9_0)
    QWebEngineLoadingInfo(const QUrl &url, QWebEngineLoadingInfo::LoadStatus status, bool isErrorPage = false, const QString &errorString = QString(), int errorCode = 0, QWebEngineLoadingInfo::ErrorDomain errorDomain = QWebEngineLoadingInfo::NoErrorDomain, const QMultiMap<QByteArray, QByteArray> &responseHeaders = {});
%End
%If (- QtWebEngine_6_6_0)
    QWebEngineLoadingInfo(const QUrl &url, QWebEngineLoadingInfo::LoadStatus status, bool isErrorPage = false, const QString &errorString = QString(), int errorCode = 0, QWebEngineLoadingInfo::ErrorDomain errorDomain = QWebEngineLoadingInfo::NoErrorDomain);
%End

public:
    QWebEngineLoadingInfo(const QWebEngineLoadingInfo &other);
    ~QWebEngineLoadingInfo();
    QUrl url() const;
    bool isErrorPage() const;
    QWebEngineLoadingInfo::LoadStatus status() const;
    QString errorString() const;
    QWebEngineLoadingInfo::ErrorDomain errorDomain() const;
    int errorCode() const;
%If (QtWebEngine_6_6_0 -)
    QMultiMap<QByteArray, QByteArray> responseHeaders() const;
%End
%If (QtWebEngine_6_9_0 -)
    bool isDownload() const;
%End
};
