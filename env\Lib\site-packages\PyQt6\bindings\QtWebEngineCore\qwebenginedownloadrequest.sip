// qwebenginedownloadrequest.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file <PERSON><PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineDownloadRequest : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebenginedownloadrequest.h>
%End

public:
    virtual ~QWebEngineDownloadRequest();

    enum DownloadState
    {
        DownloadRequested,
        DownloadInProgress,
        DownloadCompleted,
        DownloadCancelled,
        DownloadInterrupted,
    };

    quint32 id() const;
    QWebEngineDownloadRequest::DownloadState state() const;
    qint64 totalBytes() const;
    qint64 receivedBytes() const;
    QUrl url() const;
    bool isFinished() const;

public slots:
    void accept();
    void cancel();
    void pause();
    void resume();

public:
    QString mimeType() const;

    enum SavePageFormat
    {
        UnknownSaveFormat,
        SingleHtmlSaveFormat,
        CompleteHtmlSaveFormat,
        MimeHtmlSaveFormat,
    };

    QWebEngineDownloadRequest::SavePageFormat savePageFormat() const;
    void setSavePageFormat(QWebEngineDownloadRequest::SavePageFormat format);

    enum DownloadInterruptReason
    {
        NoReason,
        FileFailed,
        FileAccessDenied,
        FileNoSpace,
        FileNameTooLong,
        FileTooLarge,
        FileVirusInfected,
        FileTransientError,
        FileBlocked,
        FileSecurityCheckFailed,
        FileTooShort,
        FileHashMismatch,
        NetworkFailed,
        NetworkTimeout,
        NetworkDisconnected,
        NetworkServerDown,
        NetworkInvalidRequest,
        ServerFailed,
        ServerBadContent,
        ServerUnauthorized,
        ServerCertProblem,
        ServerForbidden,
        ServerUnreachable,
        UserCanceled,
    };

    QWebEngineDownloadRequest::DownloadInterruptReason interruptReason() const;
    QString interruptReasonString() const;
    bool isPaused() const;
    bool isSavePageDownload() const;
    QWebEnginePage *page() const;
    QString suggestedFileName() const;
    QString downloadDirectory() const;
    void setDownloadDirectory(const QString &directory);
    QString downloadFileName() const;
    void setDownloadFileName(const QString &fileName);

signals:
    void stateChanged(QWebEngineDownloadRequest::DownloadState state);
    void savePageFormatChanged();
    void receivedBytesChanged();
    void totalBytesChanged();
    void interruptReasonChanged();
    void isFinishedChanged();
    void isPausedChanged();
    void downloadDirectoryChanged();
    void downloadFileNameChanged();
};
