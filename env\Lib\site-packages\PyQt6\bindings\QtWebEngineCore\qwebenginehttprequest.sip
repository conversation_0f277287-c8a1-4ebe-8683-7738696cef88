// qwebenginehttprequest.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-WebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineHttpRequest
{
%TypeHeaderCode
#include <qwebenginehttprequest.h>
%End

public:
    enum Method
    {
        Get,
        Post,
    };

    QWebEngineHttpRequest(const QUrl &url = QUrl(), const QWebEngineHttpRequest::Method &method = QWebEngineHttpRequest::Get);
    QWebEngineHttpRequest(const QWebEngineHttpRequest &other);
    ~QWebEngineHttpRequest();
    static QWebEngineHttpRequest postRequest(const QUrl &url, const QMap<QString, QString> &postData);
    void swap(QWebEngineHttpRequest &other);
    bool operator==(const QWebEngineHttpRequest &other) const;
    bool operator!=(const QWebEngineHttpRequest &other) const;
    QWebEngineHttpRequest::Method method() const;
    void setMethod(QWebEngineHttpRequest::Method method);
    QUrl url() const;
    void setUrl(const QUrl &url);
    QByteArray postData() const;
    void setPostData(const QByteArray &postData);
    bool hasHeader(const QByteArray &headerName) const;
    QByteArray header(const QByteArray &headerName) const;
    void setHeader(const QByteArray &headerName, const QByteArray &value);
    void unsetHeader(const QByteArray &headerName);
    QList<QByteArray> headers() const;
};
