{"_comment": "OAuth2 Configuration for Microsoft SMTP Authentication", "_instructions_personal": ["FOR PERSONAL ACCOUNTS (@hotmail.com, @live.com, @outlook.com):", "1. Register your application in Azure AD (https://portal.azure.com)", "2. Go to Azure Active Directory > App registrations > New registration", "3. Set 'Supported account types' to 'Personal Microsoft accounts only'", "4. After registration, note the 'Application (client) ID'", "5. Go to 'API permissions' and add 'SMTP.Send' DELEGATED permission for Office 365 Exchange Online", "6. NO client secret needed for personal accounts", "7. Set account_type to 'personal' below", "8. Fill in the values below and rename this file to 'oauth2_config.json'"], "_instructions_business": ["FOR BUSINESS ACCOUNTS (Microsoft 365/Office 365):", "1. Register your application in Azure AD (https://portal.azure.com)", "2. Go to Azure Active Directory > App registrations > New registration", "3. Set 'Supported account types' to 'Accounts in this organizational directory only'", "4. After registration, note the 'Application (client) ID' and 'Directory (tenant) ID'", "5. Go to 'Certificates & secrets' and create a new client secret", "6. Go to 'API permissions' and add 'SMTP.SendAsApp' APPLICATION permission for Office 365 Exchange Online", "7. Grant admin consent for the permissions", "8. Set account_type to 'business' below", "9. Fill in ALL values below and rename this file to 'oauth2_config.json'"], "account_type": "personal", "client_id": "YOUR_NEW_CLIENT_ID_HERE"}