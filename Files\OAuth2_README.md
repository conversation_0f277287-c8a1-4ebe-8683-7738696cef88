# OAuth2 Authentication for Microsoft SMTP

This document explains how to set up and use OAuth2 authentication for Microsoft email accounts (Outlook/Hotmail/Live) SMTP connections.

## Background

Microsoft has disabled basic authentication for Outlook/Hotmail SMTP servers as of September 2024. The error "basic authentication is disabled" indicates that you need to use OAuth2 authentication instead.

## Account Types

There are two types of Microsoft accounts with different setup requirements:

### Personal Accounts (FREE)
- **Domains**: @hotmail.com, @live.com, @outlook.com, @msn.com
- **Setup**: Simpler - no client secret or tenant ID needed
- **Authentication**: Interactive device code flow
- **Permissions**: Microsoft Graph delegated permissions

### Business Accounts (Microsoft 365/Office 365)
- **Domains**: Custom domains managed by organizations
- **Setup**: More complex - requires client secret and tenant ID
- **Authentication**: Client credentials flow
- **Permissions**: Exchange Online application permissions

## What's Changed

The SMTP implementation now automatically detects Microsoft domains and uses OAuth2 authentication:

- **Microsoft domains**: outlook.com, hotmail.com, live.com, msn.com, office365.com
- **Authentication method**: OAuth2 with XOAUTH2 SASL
- **Fallback**: App passwords (if <PERSON>A<PERSON><PERSON> fails)
- **Other domains**: Continue using basic authentication

## Setup Instructions

### Step 1: Install Dependencies

Run the setup script to install required dependencies:

```bash
python Files/setup_oauth2.py
```

Or install manually:

```bash
pip install msal
```

### Step 2: Register Azure AD Application

**For Personal Accounts (@hotmail.com, @live.com, @outlook.com):**

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Click **New registration**
4. Fill in the details:
   - **Name**: Your application name (e.g., "Personal Email Sender")
   - **Supported account types**: **"Personal Microsoft accounts only"**
   - **Redirect URI**: Leave blank
5. Click **Register**
6. Note down the **Application (client) ID** (that's all you need!)

**For Business Accounts (Microsoft 365):**

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Click **New registration**
4. Fill in the details:
   - **Name**: Your application name (e.g., "Business SMTP Sender")
   - **Supported account types**: **"Accounts in this organizational directory only"**
   - **Redirect URI**: Leave blank or use `http://localhost`
5. Click **Register**
6. Note down the **Application (client) ID** and **Directory (tenant) ID**

### Step 3: Create Client Secret (Business Accounts Only)

**Skip this step for personal accounts!**

1. In your registered application, go to **Certificates & secrets**
2. Click **New client secret**
3. Add a description and set expiration
4. Click **Add**
5. **Important**: Copy the secret **Value** immediately (it won't be shown again)

secret value: ****************************************

secret id : 43ed82ee-7daa-4a0d-8fae-8c0dda5f35b4

### Step 4: Configure API Permissions

**For Personal Microsoft Accounts (@hotmail.com, @live.com, @outlook.com):**

1. Go to **API permissions**
2. Click **Add a permission**
3. Select **Microsoft Graph**
4. Click **Delegated permissions** (not Application permissions)
5. Expand **Mail** section
6. Select **Mail.Send** permission
7. Click **Add permissions**
8. **Note**: Admin consent is not required for personal accounts with delegated permissions

**For Business/Organization Accounts (Microsoft 365):**

1. Go to **API permissions**
2. Click **Add a permission**
3. Select **APIs my organization uses**
4. Search for "Office 365 Exchange Online"
5. Click **Application permissions**
6. Select **SMTP.SendAsApp** permission
7. Click **Add permissions**
8. **Important**: Click **Grant admin consent** for your organization


### Step 5: Configure OAuth2 Credentials

Choose one of these methods:

#### Method A: Configuration File (Recommended)

1. Copy `oauth2_config_sample.json` to `oauth2_config.json`
2. Fill in your credentials based on account type:

**For Personal Accounts:**
```json
{
    "account_type": "personal",
    "client_id": "your-application-client-id-here"
}
```

**For Business Accounts:**
```json
{
    "account_type": "business",
    "client_id": "your-application-client-id-here",
    "client_secret": "your-client-secret-here",
    "tenant_id": "your-tenant-id-here"
}
```

#### Method B: Environment Variables

**For Personal Accounts:**
```bash
export MICROSOFT_CLIENT_ID="your-client-id"
export MICROSOFT_ACCOUNT_TYPE="personal"
```

**For Business Accounts:**
```bash
export MICROSOFT_CLIENT_ID="your-client-id"
export MICROSOFT_CLIENT_SECRET="your-client-secret"
export MICROSOFT_TENANT_ID="your-tenant-id"
export MICROSOFT_ACCOUNT_TYPE="business"
```

## Usage

The OAuth2 authentication is now automatic. When you use an email address from a Microsoft domain:

1. The system detects it's a Microsoft domain
2. Attempts OAuth2 authentication first
3. Falls back to app password if OAuth2 fails
4. Provides detailed error messages and setup guidance

### Authentication Flow

**For Personal Accounts:**
- First time: Interactive device code flow
  - You'll see a message with a URL and code
  - Go to the URL, enter the code, and sign in
  - Future authentications use cached tokens (automatic)

**For Business Accounts:**
- Automatic client credentials flow
- No user interaction required
- Uses service principal authentication

## Troubleshooting

### Common Issues

#### "MSAL library not available"
- **Solution**: Install MSAL with `pip install msal`

#### "OAuth2 not properly configured"
- **Solution**: Check your configuration file or environment variables
- Ensure all three values (client_id, client_secret, tenant_id) are set

#### "Failed to acquire OAuth2 token"
- **Solution**: Verify your Azure AD application configuration
- Check that admin consent has been granted
- Ensure the client secret hasn't expired

#### "535 5.7.3 Authentication unsuccessful"
- **Solution**: This usually means:
  - Incorrect client credentials
  - Missing or incorrect API permissions
  - Admin consent not granted

### Testing OAuth2 Setup

Run the setup script with test option:

```bash
python Files/setup_oauth2.py
```

Or test manually in Python:

```python
from smtp import OAuth2SMTPAuth

oauth2_handler = OAuth2SMTPAuth()
oauth2_handler.configure_oauth2()
token = oauth2_handler.get_access_token()
print(f"Token obtained: {len(token)} characters")
```

## App Password Fallback

If OAuth2 fails, the system will attempt to use app passwords:

1. Go to [Microsoft Account Security](https://account.microsoft.com/security)
2. Enable 2-step verification if not already enabled
3. Generate an app password
4. Use the app password instead of your regular password

## Security Notes

- OAuth2 tokens are temporary and automatically refreshed
- Client secrets should be kept secure and rotated regularly
- App passwords should be used only as a fallback
- The implementation uses the client credentials flow for service applications

## Support

If you encounter issues:

1. Check the detailed error messages in the application logs
2. Verify your Azure AD application configuration
3. Ensure all permissions are granted and consented
4. Test with the provided setup script

For more information, see Microsoft's official documentation:
- [OAuth2 for SMTP](https://learn.microsoft.com/en-us/exchange/client-developer/legacy-protocols/how-to-authenticate-an-imap-pop-smtp-application-by-using-oauth)
- [Azure AD App Registration](https://learn.microsoft.com/en-us/azure/active-directory/develop/quickstart-register-app)
